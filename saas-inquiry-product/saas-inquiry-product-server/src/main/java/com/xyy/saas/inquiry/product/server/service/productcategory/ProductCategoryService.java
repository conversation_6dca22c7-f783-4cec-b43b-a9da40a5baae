package com.xyy.saas.inquiry.product.server.service.productcategory;

import java.util.*;
import com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo.ProductCategoryListReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo.ProductCategoryRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo.ProductCategorySaveReqVO;
import jakarta.validation.*;
import com.xyy.saas.inquiry.product.server.dal.dataobject.category.ProductCategoryDO;

/**
 * 商品六级分类 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductCategoryService {

    /**
     * 批量保存或更新商品六级分类
     * @param productCategories
     */
    void batchSaveOrUpdate(List<ProductCategoryDO> productCategories);

    /**
     * 创建商品六级分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProductCategory(@Valid ProductCategorySaveReqVO createReqVO);

    /**
     * 更新商品六级分类
     *
     * @param updateReqVO 更新信息
     */
    int updateProductCategory(@Valid ProductCategorySaveReqVO updateReqVO);

    /**
     * 删除商品六级分类
     *
     * @param id 编号
     */
    void deleteProductCategory(Long id);

    /**
     * 获得商品六级分类
     *
     * @param id 编号
     * @return 商品六级分类
     */
    ProductCategoryDO getProductCategory(Long id);

    /**
     * 获得商品六级分类列表
     *
     * @param listReqVO 查询条件
     * @return 商品六级分类列表
     */
    List<ProductCategoryDO> getProductCategoryList(ProductCategoryListReqVO listReqVO);

    /**
     * 清空缓存
     */
    void clearProductCategoryTreeCache();
    /**
     * 获得商品六级分类树（只返回根分类）
     *
     * @return 根分类列表
     */
    List<ProductCategoryRespVO> getProductCategoryTree();

    /**
     * 获取根分类（第1级分类）
     *
     * @return 根分类列表
     */
    List<ProductCategoryRespVO> getRootCategories();

    /**
     * 获取指定父分类的子分类
     *
     * @param parentDictId 父分类ID
     * @return 子分类列表
     */
    List<ProductCategoryRespVO> getChildCategories(Long parentDictId);

    /**
     * 获取分类详情
     *
     * @param dictId 分类字典ID
     * @return 分类详情
     */
    ProductCategoryRespVO getCategoryDetail(Long dictId);

    /**
     * 构建分类树（按需加载，支持指定深度）
     *
     * @param parentDictId 父分类ID，null表示从根开始
     * @param maxDepth 最大深度
     * @return 分类树
     */
    List<ProductCategoryRespVO> getProductCategoryTreeWithChildren(Long parentDictId, int maxDepth);

}