package com.xyy.saas.inquiry.product.server.service.product.mid;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.product.api.mid.dto.SaasCategoryDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.server.config.forward.dto.*;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.StdlibProductMixedPageQueryVo;
import jakarta.annotation.Nonnull;
import java.util.List;
import java.util.Map;

/**
 * desc 中台商品标准库服务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public interface ProductMidStdlibService {

    /**
     * 中台标准库信息查询（快速录入）
     * @param queryVo
     * @param isFilterSauAttr
     * @return
     */
    PageResult<MidSaasWithMixSearchInfoDto> fuzzySearchPlus(StdlibProductMixedPageQueryVo queryVo, boolean isFilterSauAttr);

    /**
     * 中台标准库信息查询（根据标准库id列表查询）
     * @param midStdlibIdList
     * @return
     */
    List<MidSaasWithMixSearchInfoDto> getInfoByStandardLibIdList(List<Long> midStdlibIdList);

    /**
     * 中台标准库匹配
     * @param dtoList
     * @return
     */
    List<MidGeneralMatchProductNewDto> getGeneralMatchProductNew(List<ProductInfoDto> dtoList);

    /**
     * 新品上报中台
     * @param dto
     * @return
     */
    MidResponse2<List<MidGeneralProductPresentDto>> addProductPresent(@Nonnull ProductInfoDto dto);

    /**
     * 三方外采上报中台（智鹿）
     * @param dto
     * @return 返回错误信息，为空则成功
     */
    String addProductPresentAndErpInfo(@Nonnull ProductInfoDto dto);

    /**
     * 批量查询中台标品信息（包含用法、用药频次、单次使用剂量等）
     * @param midStdlibIdList
     * @return
     */
    List<MidGeneralProductDto> getGeneralProductList(List<Long> midStdlibIdList);

    /**
     * 批量查询中台标品图片
     * @param midStdlibIdList
     * @return
     */
    List<MidPictureProResult> findProPicture(List<Long> midStdlibIdList);

    /**
     * 批量查询中台字典数据
     * @param vo
     * @return
     */
    PageResult<MidTotalDictionaryReadDto> pageQueryDictionaryUpgrade(MidDictionaryVo vo);

    /**
     * 通过上级 id 获取子级分类
     * @param parentCategoryId 传空或者 0 获取第一级分类
     * @return list
     */
    List<SaasCategoryDto> queryCategoryByParentId(Integer parentCategoryId);


    /**
     * 通过商品名称获取商品六级分类
     * @param productName  商品名称
     * @return list
     */
    List<List<SaasCategoryDto>> queryProductCategoryByProductName(String productName);


    /**
     *  通过商品名称获取商品六级分类
     * @param productNames 商品名称列表
     * @return map
     */
    Map<String, List<List<SaasCategoryDto>>> queryProductCategoryByProductNameList(List<String> productNames);


    /**
     * 根据id 查询分类链路
     * @param endIds 末级分类id
     * @return
     */
    Map<Integer, List<List<SaasCategoryDto>>> queryCategoryPathByIds(List<Integer> endIds);

    /**
     *  分类名称模糊匹配上级链路 - 分页
     * @param name
     * @param pageNum 从0开始
     * @param pageSize
     * @return
     */
    PageResult<List<SaasCategoryDto>> queryCategoryPathByNameFuzzy(String name, Integer pageNum, Integer pageSize);


    /**
     * 根据六级分类名称查看分类链路
     * @param names
     * @return
     */
    Map<String, List<List<SaasCategoryDto>>> queryCategoryPathByNames(List<String> names);
}
