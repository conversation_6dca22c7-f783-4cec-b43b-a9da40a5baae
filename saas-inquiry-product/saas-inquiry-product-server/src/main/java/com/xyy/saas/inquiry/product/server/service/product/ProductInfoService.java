package com.xyy.saas.inquiry.product.server.service.product;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoUpdateDto;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductFlagUpdateReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductInfoPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductInfoRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductPriceUpdateReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductUnbundledBatchSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductUnbundledSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.product.vo.ProductPresentSaveReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductInfoDO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 商品基本信息 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductInfoService {

    /**
     * 保存（新增 or 修改）商品基本信息
     *
     * @param dto           创建信息
     * @param bizType       业务类型
     * @return 编号
     */
    Long saveOrUpdateProduct(ProductInfoDto dto, ProductBizTypeEnum bizType);

    /**
     * 更新商品信息（状态、删除类型）
     * @param dto
     */
    void updateProductInfo(ProductInfoUpdateDto dto);

    /**
     * 批量修改 商品属性标志
     *
     * @param updateReqVO   修改信息
     * @return 编号列表
     */
    List<Long> updateProductFlag(ProductFlagUpdateReqVO updateReqVO);

    /**
     * 修改商品价格
     *
     * @param updateReqVO   价格修改信息
     */
    void updateProductPrice(ProductPriceUpdateReqVO updateReqVO);

    /**
     * 删除商品基本信息
     *
     * @param id 编号
     */
    void deleteProductInfo(Long id);

    /**
     * 获得商品基本信息（带标准库信息，使用信息，资质信息）,
     * 不传租户信息，则按照商品所属租户组装使用信息（医保信息、价格）
     *  getCurrentTenantInfo 默认 false
     *
     * @param id 编号
     * @return 商品基本信息
     */
    ProductInfoDto getProductInfo(Long id, boolean getCurrentTenantInfo);

    /**
     * 获得商品基本信息（带标准库信息，使用信息，资质信息）,
     * 传租户信息，则按照所传租户组装使用信息（医保信息、价格）
     *
     * @param id 编号
     * @return 商品基本信息
     */
    ProductInfoDto getProductInfo(Long id, TenantDto tenant);

    /**
     * 获得商品基本信息,
     * 不传租户信息，则按照商品所属租户组装使用信息（医保信息、价格）
     *  getCurrentTenantInfo 默认 false
     *
     * @param prefList 编号
     * @return 商品基本信息
     */
    List<ProductInfoDto> listProductInfoByPref(List<String> prefList, boolean getCurrentTenantInfo);

    /**
     * 获得商品基本信息,
     * 传租户信息，则按照所传租户组装使用信息（医保信息、价格）
     *
     * @param prefList 编号
     * @return 商品基本信息
     */
    List<ProductInfoDto> listProductInfoByPref(List<String> prefList, TenantDto tenant);

    /**
     * 验证租户商品总数量限制（包含逻辑删除的数据）
     * @param tenantId
     * @param addCount
     * @return
     */
    void validTenantProductCountLimit(Long tenantId, long addCount);

    /**
     * 获得商品基本信息分页
     *
     * @param pageReqVO 分页查询
     * @return 商品基本信息分页
     */
    PageResult<ProductInfoDto> getProductInfoPage(ProductInfoPageReqVO pageReqVO);

    /**
     * 获得商品基本信息分页（关联审批信息）
     *
     * @param pageReqVO 分页查询
     * @return 商品基本信息分页
     */
    PageResult<ProductInfoRespVO> getProductInfoWithBizRltPage(ProductInfoPageReqVO pageReqVO);


    /**
     * 创建拆零商品
     *
     * @param batchSaveReqVO 创建信息
     * @return 编号
     */
    List<Long> saveOrUpdateUnbundledProduct(@Valid ProductUnbundledBatchSaveReqVO batchSaveReqVO);


    /**
     * 保存商品提报
     *
     * @param reqVO
     * @return  返回门店商品id
     */
    Long saveProductPresent(ProductPresentSaveReqVO reqVO);

}