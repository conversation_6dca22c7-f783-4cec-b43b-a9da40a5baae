package com.xyy.saas.inquiry.product.server.controller.admin.product.vo;

import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.product.api.product.dto.ProductFlag;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductUpdateDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotEmpty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;


@Data
@Schema(description = "管理后台 - 标准库商品批量更新请求 VO")
public class StdlibProductUpdateReqVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "id列表", requiredMode = RequiredMode.REQUIRED, example = "[1,2,3]")
    @NotEmpty(message = "id列表不能为空")
    private List<Long> idList;

    @Schema(description = "状态", requiredMode = RequiredMode.REQUIRED, example = "true")
    private Integer status;

    @Schema(description = "停用状态", requiredMode = RequiredMode.REQUIRED, example = "true")
    private Boolean disable;

    @Schema(description = "中台同步是否跳过（不覆盖）", requiredMode = RequiredMode.REQUIRED, example = "true")
    private Boolean midSyncSkipped;


    /**
     * VO 转换 Dto
     * @return
     */
    public StdlibProductUpdateDto Vo2Dto() {
        StdlibProductUpdateDto dto = BeanUtils.toBean(this, StdlibProductUpdateDto.class);
        if (this.midSyncSkipped != null) {
            dto.setProductFlag(new ProductFlag()
                .setMidSyncSkipped(this.midSyncSkipped));
        }
        return dto;
    }
}
