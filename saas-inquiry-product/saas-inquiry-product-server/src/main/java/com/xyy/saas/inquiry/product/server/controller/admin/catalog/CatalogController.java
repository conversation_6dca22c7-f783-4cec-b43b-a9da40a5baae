package com.xyy.saas.inquiry.product.server.controller.admin.catalog;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.inquiry.pojo.catalog.CatalogRelationTenantDto;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogSaveReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.CatalogDO;
import com.xyy.saas.inquiry.product.server.service.catalog.CatalogService;
import com.xyy.saas.inquiry.product.server.service.catalog.MedicalCatalogDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 目录")
@RestController
@RequestMapping("/product/catalog")
@Validated
public class CatalogController {

    @Resource
    private CatalogService catalogService;

    @Resource
    private MedicalCatalogDetailService medicalCatalogDetailService;

    @PostMapping("/create")
    @Operation(summary = "创建目录")
    @PreAuthorize("@ss.hasPermission('saas:product:catalog:create')")
    public CommonResult<CatalogRespVO> createCatalog(@Valid @RequestBody CatalogSaveReqVO createReqVO) {
        return success(catalogService.createCatalog(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新目录")
    @PreAuthorize("@ss.hasPermission('saas:product:catalog:update')")
    public CommonResult<Boolean> updateCatalog(@Valid @RequestBody CatalogSaveReqVO updateReqVO) {
        catalogService.updateCatalog(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除目录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:product:catalog:delete')")
    public CommonResult<Boolean> deleteCatalog(@RequestParam("id") Long id) {
        catalogService.deleteCatalog(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得目录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:product:catalog:query')")
    public CommonResult<CatalogRespVO> getCatalog(@RequestParam("id") Long id) {
        CatalogDO catalog = catalogService.getCatalog(id);
        return success(BeanUtils.toBean(catalog, CatalogRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得目录分页")
    @PreAuthorize("@ss.hasPermission('saas:product:catalog:query')")
    public CommonResult<PageResult<CatalogRespVO>> getCatalogPage(@Valid @ParameterObject CatalogPageReqVO pageReqVO) {

        return success(catalogService.getCatalogPage(pageReqVO));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出目录 Excel")
    @PreAuthorize("@ss.hasPermission('saas:product:catalog:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCatalogExcel(@Valid CatalogPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CatalogRespVO> list = catalogService.getCatalogPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "目录.xls", "数据", CatalogRespVO.class, list);
    }

    @GetMapping("/page-version")
    @Operation(summary = "获得目录分页")
    @PreAuthorize("@ss.hasPermission('saas:product:catalog:query')")
    public CommonResult<PageResult<CatalogRespVO>> getCatalogVersionPage(@Valid @ParameterObject CatalogPageReqVO pageReqVO) {
        PageResult<CatalogRespVO> pageResult = catalogService.getCatalogVersionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CatalogRespVO.class));
    }

    @PostMapping("/page-relation-tenant")
    @Operation(summary = "获得目录关联的租户")
    @PreAuthorize("@ss.hasPermission('saas:product:catalog:query')")
    public CommonResult<PageResult<CatalogRelationTenantDto>> pageRelationTenant(@Valid @RequestBody CatalogPageReqVO pageReqVO) {
        return success(catalogService.pageRelationTenant(pageReqVO));
    }

    @PostMapping("/match-stdlib")
    @Operation(summary = "触发目录匹配自建标准库")
    @PreAuthorize("@ss.hasPermission('saas:product:catalog:update')")
    public CommonResult<String> matchCatalogStdlib(@RequestParam("catalogId") Long catalogId) {
        if (catalogId == null) {
            throw new IllegalArgumentException("catalogId不能为空");
        }
        
        // 异步执行匹配任务
        medicalCatalogDetailService.asyncMatchCatalogStdlib(catalogId, null);

        return success("匹配任务已提交，正在后台处理");
    }

}