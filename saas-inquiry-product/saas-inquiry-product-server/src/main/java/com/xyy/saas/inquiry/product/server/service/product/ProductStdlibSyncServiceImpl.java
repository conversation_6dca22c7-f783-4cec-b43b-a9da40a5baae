package com.xyy.saas.inquiry.product.server.service.product;

import static com.xyy.saas.inquiry.product.server.dal.redis.RedisKeyConstants.PRODUCT_STDLIB_SYNC_LOCK_KEY;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibSyncDto;
import com.xyy.saas.inquiry.product.server.config.properties.ProductStdlibSyncProperties;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.StdlibProductSyncReqVo;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibSyncDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductStdlibSyncMapper;
import com.xyy.saas.inquiry.product.server.enums.StdlibSyncStatusEnum;
import com.xyy.saas.inquiry.product.server.enums.StdlibSyncTypeEnum;
import com.xyy.saas.inquiry.util.RedisUtils;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.LongStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class ProductStdlibSyncServiceImpl implements ProductStdlibSyncService {

    @Resource
    private ProductStdlibSyncMapper stdlibSyncMapper;

    @Resource
    private ProductStdlibService stdlibService;

    @Resource
    private ProductStdlibSyncProperties stdlibSyncProperties;

    @Resource
    @Lazy
    private ProductStdlibSyncService selfProxy;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startSingleSync(StdlibProductSyncReqVo reqVo) {
        if (reqVo == null || CollectionUtils.isEmpty(reqVo.getMidStdlibIdList())) {
            return;
        }
        List<Long> midStdlibIdList = reqVo.getMidStdlibIdList();
        // 1. 保存同步记录
        List<ProductStdlibSyncDO> progressDOList = midStdlibIdList.stream().map(midStdlibId -> {
            String guid = UUID.randomUUID().toString();
            return new ProductStdlibSyncDO()
                .setGuid(guid)
                .setType(StdlibSyncTypeEnum.SINGLE.getCode())
                .setStatus(StdlibSyncStatusEnum.NOT_START.getCode())
                .setStartTime(LocalDateTime.now())
                .setStartId(midStdlibId)
                .setEndId(midStdlibId)
                .setCurrent(midStdlibId);
        }).toList();
        stdlibSyncMapper.insertBatch(progressDOList);

        // 2. 保存中台数据
        List<ProductStdlibDO> productStdlibDOS = stdlibService.saveOrUpdateStdlibFromMid(midStdlibIdList, true);
        log.info("[startSingleSync][单个同步完成]: midStdlibIdList={}, size={}", midStdlibIdList, productStdlibDOS.size());

        Map<Long, ProductStdlibDO> midStdlibIdMap = productStdlibDOS.stream().collect(Collectors.toMap(ProductStdlibDO::getMidStdlibId, Function.identity(), (oldValue, newValue) -> oldValue));
        progressDOList.forEach(productStdlibSyncDO -> {
            ProductStdlibDO productStdlibDO = midStdlibIdMap.get(productStdlibSyncDO.getCurrent());
            if (productStdlibDO != null) {
                productStdlibSyncDO.setStatus(StdlibSyncStatusEnum.COMPLETED.getCode());
            } else {
                productStdlibSyncDO.setStatus(StdlibSyncStatusEnum.FAILED.getCode())
                    .setErrorMsg("中台商品不存在或跳过更新");
            }
            productStdlibSyncDO.setEndTime(LocalDateTime.now());
        });
        // 2. 更新同步状态
        stdlibSyncMapper.updateById(progressDOList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProductStdlibSyncDto startFullSync() {
        // 1. 检查是否开启全量同步
        if (!stdlibSyncProperties.isEnableFullSync()) {
            log.warn("[startFullSync][全量同步开关未开启]，跳过！");
            return null;
        }
        // 关键节点增加日志输出
        log.info("[startFullSync][开始全量同步]");

        // 2. 检查是否有正在进行中的任务
        List<ProductStdlibSyncDO> runningSyncList = stdlibSyncMapper.selectRunningFullSync();

        if (CollectionUtils.isNotEmpty(runningSyncList)) {
            ProductStdlibSyncDO runningSync = runningSyncList.getFirst();
            log.info("[startFullSync][存在进行中的任务,直接返回]: {}", runningSync);
            return BeanUtils.toBean(runningSync, ProductStdlibSyncDto.class);
        }

        // 3. 生成任务ID
        String guid = UUID.randomUUID().toString();
        
        // 4. 初始化进度
        ProductStdlibSyncDto progress = new ProductStdlibSyncDto()
            .setGuid(guid)
            .setType(StdlibSyncTypeEnum.FULL.getCode())
            .setStatus(StdlibSyncStatusEnum.NOT_START.getCode())
            .setStartTime(LocalDateTime.now())
            .setCreateTime(LocalDateTime.now())
            .setStartId(stdlibSyncProperties.getFullSyncStartId())
            .setEndId(stdlibSyncProperties.getFullSyncEndId())
            .setCurrent(stdlibSyncProperties.getFullSyncStartId() -1);

        log.info("[startFullSync][全量同步进度初始化完成]: {}", progress);
        
        // 5. 保存进度
        selfProxy.saveOrUpdateProgress(progress);

        return progress;
    }

    @Override
    public void syncWithRetry(String guid, int retryTimes) {
        int times = Math.max(0, retryTimes) + 1;
        boolean success = false;
        // 延迟重试1次
        for (int i = 0; i < times; i++) {
            if (i > 0) {
                ThreadUtil.sleep(90000L);
                log.info("[runFullSyncWithRetry][全量同步重试]: guid={}, 重试第 {} 次", guid, i);
            }
            // redis 加锁90s
            String lockKey = PRODUCT_STDLIB_SYNC_LOCK_KEY + guid;
            String requestId = RandomStringUtils.randomAlphanumeric(16);
            if (!RedisUtils.tryLockWithRenewal(lockKey, requestId, 90_000L)) {
                log.warn("[startFullSync][全量同步锁定中]: guid={} 跳过！", guid);
                break;
            }
            try {
                success = selfProxy.syncWithCompleteProgress(guid);
                if (success) {
                    break;
                }
            } catch (Throwable e) {
                log.error("[startFullSync][全量同步异常]: msg={}", e.getMessage(), e);
                break;
            } finally {
                RedisUtils.releaseLock(lockKey, requestId);
            }
        }
        log.error("[runFullSyncWithRetry][全量同步结束]: guid={}, success: {}", guid, success);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean syncWithCompleteProgress(String guid) {
        ProductStdlibSyncDO progress = stdlibSyncMapper.selectByGuid(guid);
        if (progress == null ||
            !(StdlibSyncStatusEnum.NOT_START.getCode().equals(progress.getStatus())
                || StdlibSyncStatusEnum.RUNNING.getCode().equals(progress.getStatus()))) {
            return false;
        }

        int batchSize = stdlibSyncProperties.getFullSyncIdBatchSize();
        long startId = progress.getCurrent() + 1;
        long endId = progress.getEndId();
        if (startId > endId) {
            return true;
        }

        log.info("[startFullSync][全量同步开始]: startId={}, endId={}, batchSize={}", startId, endId, batchSize);

        long pageSize = ((endId - startId + 1) / batchSize + 1);
        log.info("[startFullSync][全量同步开始]: pageSize={}", pageSize);

        for (long i = 0; i < pageSize; i++) {
            long fullSyncInterval = stdlibSyncProperties.getFullSyncInterval();
            // 延迟一会儿，避免太快
            if (i > 0 && fullSyncInterval > 0) {
                ThreadUtil.sleep(fullSyncInterval);
            }

            progress = selfProxy.syncWithStandaloneTransaction(progress, startId + i * batchSize);
            if (!StdlibSyncStatusEnum.RUNNING.getCode().equals(progress.getStatus())) {
                break;
            }
        }

        // 失败后重试
        boolean failed = StdlibSyncStatusEnum.FAILED.getCode().equals(progress.getStatus());
        log.info("[startFullSync][全量同步完成]: {}", progress);
        return !failed;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public ProductStdlibSyncDO syncWithStandaloneTransaction(@Nonnull ProductStdlibSyncDO progress, long start) {
        int batchSize = stdlibSyncProperties.getFullSyncIdBatchSize();
        long endId = progress.getEndId();
        long end = Math.min(start + batchSize, endId + 1);
        try {
            // 修改状态：运行中
            if (Objects.equals(progress.getStatus(), StdlibSyncStatusEnum.NOT_START.getCode())) {
                progress.setStatus(StdlibSyncStatusEnum.RUNNING.getCode());
                stdlibSyncMapper.updateById(progress);
            }

            // 5.1 检查是否取消
            ProductStdlibSyncDO exist = stdlibSyncMapper.selectById(progress.getId());
            if (exist == null) {
                log.info("[startFullSync][全量同步不存在]: id={}", progress.getId());
                return progress.setStatus(StdlibSyncStatusEnum.FAILED.getCode())
                    .setErrorMsg("全量同步记录不存在");
            }
            if (Objects.equals(exist.getStatus(), StdlibSyncStatusEnum.CANCELLED.getCode())) {
                log.info("[startFullSync][全量同步被取消]: {}", exist);
                return exist;
            }
            if (exist.getCurrent() >= progress.getEndId()) {
                log.info("[startFullSync][全量同步已完成]: {}", exist);
                return exist;
            }

            // 5.2 计算本批次ID范围（需要包含endId）
            List<Long> midStdlibIdList = LongStream.range(start, end).boxed().toList();
            if (!midStdlibIdList.isEmpty()) {
                log.info("[startFullSync][全量同步处理]: midStdlibId 范围: [{}, {})", start, end);
                // 5.3 同步中台数据
                List<ProductStdlibDO> productStdlibDOS = selfProxy.saveOrUpdateStdlibFromMidWithNewTx(midStdlibIdList, true);
                log.info("[startFullSync][全量同步处理]: midStdlibId 范围: [{}, {}), size: {}", start, end, productStdlibDOS.size());
            }

            // 5.4 更新进度
            exist.setCurrent(exist.getCurrent() + midStdlibIdList.size());
            // 防止状态被覆盖（任务取消后，又改为运行中了）
            ProductStdlibSyncDO upd = new ProductStdlibSyncDO()
                .setId(exist.getId())
                .setCurrent(exist.getCurrent());
            // 5.5 检查是否完成
            if (exist.getCurrent() >= progress.getEndId()) {
                log.info("[startFullSync][全量同步已完成]: {}", exist);
                // 5.6 同步完成
                exist.setEndTime(LocalDateTime.now())
                    .setStatus(StdlibSyncStatusEnum.COMPLETED.getCode());
                upd.setEndTime(LocalDateTime.now())
                    .setStatus(StdlibSyncStatusEnum.COMPLETED.getCode());
            }

            stdlibSyncMapper.updateById(upd);
            return exist;
        } catch (Exception e) {
            log.error("[startFullSync][全量同步失败]: midStdlibId 范围: [{}, {}), msg: {}", start, end, e.getMessage(), e);
            // 5.7 同步失败
            progress.setStatus(StdlibSyncStatusEnum.FAILED.getCode())
                .setEndTime(LocalDateTime.now())
                .setErrorMsg(ExceptionUtil.stacktraceToString(e));
            stdlibSyncMapper.updateById(progress);
            return progress;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public List<ProductStdlibDO> saveOrUpdateStdlibFromMidWithNewTx(List<Long> midStdlibIdList, boolean imageSync) {
        return stdlibService.saveOrUpdateStdlibFromMid(midStdlibIdList, imageSync);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelSync(String guid) {
        // 使用乐观锁更新状态
        return stdlibSyncMapper.updateStatus(
            guid,
            StdlibSyncStatusEnum.CANCELLED.getCode(),
            StdlibSyncStatusEnum.RUNNING.getCode()
        ) > 0;
    }

    @Override
    public ProductStdlibSyncDto getProgress(String guid) {
        ProductStdlibSyncDO sync = stdlibSyncMapper.selectByGuid(guid);
        return sync != null ? BeanUtils.toBean(sync, ProductStdlibSyncDto.class) : null;
    }

    @Override
    public List<ProductStdlibSyncDto> getProgressList() {
        List<ProductStdlibSyncDO> syncList = stdlibSyncMapper.selectLatestList(50);
        return BeanUtils.toBean(syncList, ProductStdlibSyncDto.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateProgress(ProductStdlibSyncDto progress) {
        ProductStdlibSyncDO sync = BeanUtils.toBean(progress, ProductStdlibSyncDO.class);
        // MybatisPlus 的 insertOrUpdate 批量有问题
        stdlibSyncMapper.insertOrUpdate(sync);
        progress.setId(sync.getId());
    }

    @Override
    @Transactional(readOnly = true)
    public List<ProductStdlibSyncDO> runNotStartFullSyncTasks() {
        // 1. 查询所有未开始的任务
        List<ProductStdlibSyncDO> notStartTasks = stdlibSyncMapper.selectRunningFullSync();

        if (notStartTasks.isEmpty()) {
            return List.of();
        }

        log.info("[runNotStartFullSyncTasks][开始执行任务]: size={}", notStartTasks.size());

        // 2. 逐个执行任务
        for (ProductStdlibSyncDO task : notStartTasks) {
            try {
                // 2.1 执行任务(重试1次)
                selfProxy.syncWithRetry(task.getGuid(), 1);
            } catch (Exception e) {
                log.error("[runNotStartFullSyncTasks][任务执行异常]: guid={}, error={}",
                    task.getGuid(), e.getMessage(), e);
            }
        }
        return stdlibSyncMapper.selectList(ProductStdlibSyncDO::getGuid, notStartTasks.stream().map(ProductStdlibSyncDO::getGuid).toList());
    }
} 