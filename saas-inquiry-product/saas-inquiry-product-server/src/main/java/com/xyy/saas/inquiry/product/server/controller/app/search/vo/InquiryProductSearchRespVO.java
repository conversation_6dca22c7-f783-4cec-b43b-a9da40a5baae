package com.xyy.saas.inquiry.product.server.controller.app.search.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2024/11/25 20:41
 */
@Schema(description = "App - 问诊商品搜索 Resp VO")
@Data
@ToString(callSuper = true)
public class InquiryProductSearchRespVO {

    @Schema(description = "商品编码")
    private String pref;

    @Schema(description = "标准库id")
    private String standardId;

    @Schema(description = "品牌名")
    private String productName;

    @Schema(description = "商品通用名称")
    private String commonName;

    @Schema(description = "数量")
    private BigDecimal quantity;

    @Schema(description = "规格")
    private String attributeSpecification;

    @Schema(description = "生产厂家")
    private String manufacturer;

    @Schema(description = "使用频次")
    private String useFrequency;

    @Schema(description = "使用频次Value")
    private String useFrequencyValue;

    @Schema(description = "单次剂量")
    private String singleDose;

    @Schema(description = "单次剂量单位")
    private String singleUnit;

    @Schema(description = "用药方法")
    private String directions;

    @Schema(description = "批准文号")
    private String approvalNumber;

    @Schema(description = "单位名称")
    private String unitName;

    @Schema(description = "剂型")
    private String dosageForm;


    // 兼容西电医保目录匹配标准库，用法用量审查 需要传标准库信息
    @Schema(description = "标准库id（自建标准库）")
    private Long stdlibId;
    @Schema(description = "通用名称（自建标准库）")
    private String stdlibCommonName;
    @Schema(description = "规格（自建标准库）")
    private String stdlibSpec;

}
