package com.xyy.saas.inquiry.product.server.controller.admin.product.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import co.elastic.clients.elasticsearch._types.query_dsl.Like;
import com.xyy.saas.inquiry.product.api.product.dto.ProductFlag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import java.io.Serial;
import java.math.BigDecimal;
import java.util.List;


@Schema(description = "管理后台 - 标准库商品信息 分页查询请求 VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class StdlibProductPageQueryVo extends PageParam {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "自建标准库ID")
    private List<Long> idList;

    @Schema(description = "商品大类")
    private String spuCategory;

    @Schema(description = "商品信息（模糊匹配）")
    private String mixedQuery;
    @Schema(description = "商品信息（自然搜索）")
    private String natureQuery;

    @Schema(description = "商品名称（模糊匹配）")
    private String mixedNameQuery;
    @Schema(description = "商品名称（自然搜索）")
    private String natureNameQuery;

    @Schema(description = "规格型号")
    private String spec;
    @Schema(description = "处方分类")
    private String presCategory;

    @Schema(description = "条形码")
    private String barcode;
    @Schema(description = "通用名")
    private String commonName;
    @Schema(description = "生产厂家")
    private String manufacturer;
    @Schema(description = "批准文号")
    private String approvalNumber;
    @Schema(description = "中台标准库ID")
    private Long midStdlibId;

    @Schema(description = "通用名（模糊匹配）")
    private String commonNameLike;
    @Schema(description = "规格型号（模糊匹配）")
    private String specLike;
    @Schema(description = "生产厂家（模糊匹配）")
    private String manufacturerLike;
    @Schema(description = "批准文号（模糊匹配）")
    private String approvalNumberLike;
    @Schema(description = "最小包装数量")
    private BigDecimal minPackageNum;

    @Schema(description = "一级分类")
    private String firstCategory;
    @Schema(description = "二级分类")
    private String secondCategory;
    @Schema(description = "三级分类")
    private String thirdCategory;
    @Schema(description = "四级分类")
    private String fourthCategory;
    @Schema(description = "五级分类")
    private String fiveCategory;
    @Schema(description = "六级分类")
    private String sixCategory;

    @Schema(description = "属性标志")
    private ProductFlag multiFlag;
    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "停用状态")
    private Boolean disable;

}
