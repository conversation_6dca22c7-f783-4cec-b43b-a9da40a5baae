package com.xyy.saas.inquiry.product.server.service.bpm;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;
import static com.xyy.saas.inquiry.product.consts.ProductConstant.ApprovalStatus;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.BPM_BUSINESS_RELATION_NOT_EXISTS;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.BPM_BUSINESS_TYPE_NOT_EXISTS;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PARAM_INVALID;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.number.NumberUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.common.util.validation.ValidationUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.bpm.api.task.dto.BpmProcessInstanceCreateReqDTO;
import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceCancelReqVO;
import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceRespVO;
import cn.iocoder.yudao.module.bpm.convert.task.BpmProcessInstanceConvert;
import cn.iocoder.yudao.module.bpm.dal.dataobject.definition.BpmCategoryDO;
import cn.iocoder.yudao.module.bpm.service.definition.BpmCategoryService;
import cn.iocoder.yudao.module.bpm.service.definition.BpmProcessDefinitionService;
import cn.iocoder.yudao.module.bpm.service.task.BpmProcessInstanceService;
import cn.iocoder.yudao.module.bpm.service.task.BpmTaskService;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.product.api.bpm.BpmBusinessRelationDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.enums.BpmBusinessTypeEnum;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmBusinessRelationCancelReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmBusinessRelationPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmBusinessRelationRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmBusinessRelationRespVO2;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmApprovePageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmApproveRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductInfoRespVO;
import com.xyy.saas.inquiry.product.server.convert.product.ProductConvert;
import com.xyy.saas.inquiry.product.server.dal.dataobject.bpm.BpmBusinessRelationDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.bpm.BpmBusinessRelationMapper;
import com.xyy.saas.inquiry.product.server.service.product.ProductInfoService;
import jakarta.annotation.Resource;
import jakarta.validation.Validator;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.task.api.Task;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 审批流关联业务 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class BpmBusinessRelationServiceImpl implements BpmBusinessRelationService {

    @Resource
    private Validator validator;

    @Resource
    private BpmBusinessRelationMapper bpmBusinessRelationMapper;

    @Resource
    private TenantApi tenantApi;


    @Resource
    private HistoryService historyService;

    @Resource
    private BpmProcessInstanceService processInstanceService;
    @Resource
    private BpmTaskService taskService;
    @Resource
    private BpmProcessDefinitionService processDefinitionService;
    @Resource
    private BpmCategoryService categoryService;

    @Resource
    private AdminUserApi adminUserApi;
    @Resource
    private DeptApi deptApi;

    @Resource
    @Lazy
    private ProductInfoService productInfoService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createProcessInstance(Long userId, BpmBusinessRelationDto businessDto) {
        if (userId == null || businessDto == null) {
            throw exception(PARAM_INVALID, "userId | businessDto", "不能为空");
        }
        ValidationUtils.validate(validator, businessDto);

        // 校验业务类型
        Integer businessType = businessDto.getBusinessType();
        BpmBusinessTypeEnum businessTypeEnum = BpmBusinessTypeEnum.getByCode(businessType);
        if (businessTypeEnum == null) {
            throw exception(BPM_BUSINESS_TYPE_NOT_EXISTS, businessType);
        }

        // 校验业务单据是否已存在审批流
        boolean exists = bpmBusinessRelationMapper.exists(new LambdaQueryWrapperX<BpmBusinessRelationDO>()
            .eq(BpmBusinessRelationDO::getBusinessPref, businessDto.getBusinessPref())
            .eq(BpmBusinessRelationDO::getBusinessType, businessType)
            .eq(BpmBusinessRelationDO::getApprovalStatus, ApprovalStatus.RUNNING));
        if (exists) {
            return null;
        }

        // 插入 业务关联表
        BpmBusinessRelationDO businessRelationDO = BeanUtils.toBean(businessDto, BpmBusinessRelationDO.class)
            .setStartTime(LocalDateTime.now())
            .setApplicant("" + userId)
            .setApprovalStatus(ApprovalStatus.RUNNING);
        bpmBusinessRelationMapper.insert(businessRelationDO);
        // 业务关联表id
        Long brId = businessRelationDO.getId();

        // 发起 BPM 流程
        Map<String, Object> processInstanceVariables = new HashMap<>();
        // processInstanceVariables.put("day", day);
        String processInstanceId = processInstanceService.createProcessInstance(userId,
            new BpmProcessInstanceCreateReqDTO().setProcessDefinitionKey(businessTypeEnum.processDefinitionKey)
                .setVariables(processInstanceVariables)
                .setBusinessKey(String.valueOf(brId))
                // .setStartUserSelectAssignees(createReqVO.getStartUserSelectAssignees())
            );

        // 将工作流的编号，更新到 业务关联表中
        bpmBusinessRelationMapper.updateById(new BpmBusinessRelationDO().setId(brId).setProcessInstanceId(processInstanceId));
        return brId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BpmBusinessRelationDO updateProcessInstanceStatus(BpmBusinessRelationDto businessDto) {
        Long id = businessDto.getId();
        // 校验存在
        BpmBusinessRelationDO businessRelationDO = validateBpmBusinessRelationExists(id);
        // 更新审批关联表状态
        BpmBusinessRelationDO updateObj = new BpmBusinessRelationDO()
            .setId(id)
            .setApprovalStatus(businessDto.getApprovalStatus());
        bpmBusinessRelationMapper.updateById(updateObj);

        return businessRelationDO.setApprovalStatus(updateObj.getApprovalStatus());
    }

    @Override
    public void deleteProcessInstance(BpmBusinessRelationCancelReqVO cancelReqVO) {
        ValidationUtils.validate(validator, cancelReqVO);

        // 查询审批中中的审批流
        List<BpmBusinessRelationDO> runningBprList = bpmBusinessRelationMapper.selectList(new LambdaQueryWrapperX<BpmBusinessRelationDO>()
            .in(BpmBusinessRelationDO::getBusinessPref, cancelReqVO.getBusinessPrefList())
            .eqIfPresent(BpmBusinessRelationDO::getBusinessType, cancelReqVO.getBusinessType())
            .eq(BpmBusinessRelationDO::getApprovalStatus, ApprovalStatus.RUNNING));

        // 获取当前管理员
        Long tenantId = cancelReqVO.getTenantId();
        TenantDto tenantDto = tenantId == null ? tenantApi.getTenant() : tenantApi.getTenant(tenantId);
        Long userId = tenantDto.getContactUserId();
        for (BpmBusinessRelationDO runningBpr : runningBprList) {
            // 取消审批流
            processInstanceService.cancelProcessInstanceByAdmin(userId, new BpmProcessInstanceCancelReqVO()
                .setId(runningBpr.getProcessInstanceId())
                .setReason(cancelReqVO.getReason()));
            log.info("[deleteProcessInstance][取消审批流] userId({}) tenantId({}) processInstanceId({})",
                userId, tenantId, runningBpr.getProcessInstanceId());

            // 删除审批关联表
            bpmBusinessRelationMapper.deleteById(runningBpr);
            log.info("[deleteProcessInstance][删除审批业务关联表] userId({}) tenantId({}) businessRelationId({})",
                userId, tenantId, runningBpr.getId());
        }

    }

    private BpmBusinessRelationDO validateBpmBusinessRelationExists(Long id) {
        BpmBusinessRelationDO businessRelationDO = bpmBusinessRelationMapper.selectById(id);
        if (businessRelationDO == null) {
            throw exception(BPM_BUSINESS_RELATION_NOT_EXISTS);
        }
        return businessRelationDO;
    }

    @Override
    public BpmBusinessRelationDO getBpmBusinessRelation(Long id) {
        return bpmBusinessRelationMapper.selectById(id);
    }

    /**
     * 获得审批流关联业务
     *
     * @param processInstanceId 编号
     * @return 审批流关联业务
     */
    @Override
    public BpmBusinessRelationDO getBpmBusinessRelation(String processInstanceId) {
        return bpmBusinessRelationMapper.selectOne(BpmBusinessRelationDO::getProcessInstanceId, processInstanceId);
    }

    @Override
    public PageResult<BpmBusinessRelationDO> getBpmBusinessRelationPage(BpmBusinessRelationPageReqVO pageReqVO) {
        // 设置租户id
        if (pageReqVO.getHeadTenantId() == null) {
            TenantDto tenantDto = TenantContextHolder.getTenantContextInfo(TenantConstant.TENANT_CONTEXT_KEY_TENANT_DTO);
            pageReqVO.setHeadTenantId(tenantDto.getHeadTenantId());
        }
        return bpmBusinessRelationMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<BpmBusinessRelationRespVO2> getBpmBusinessRelationPage2(BpmBusinessRelationPageReqVO pageReqVO) {
        // 获得分页
        PageResult<BpmBusinessRelationDO> pageResult = getBpmBusinessRelationPage(pageReqVO);

        if (CollUtil.isEmpty(pageResult.getList())) {
            return PageResult.empty(pageResult.getTotal());
        }

        Set<String> processInstanceIdList = convertSet(pageResult.getList(), BpmBusinessRelationDO::getProcessInstanceId);
        List<BpmProcessInstanceRespVO> bpmProcessInstanceRespVOList = buildProcessInstanceList(processInstanceIdList);

        // 组装 业务数据 businessRelation
        Map<String, BpmBusinessRelationDO> businessRelationDOMap = pageResult.getList().stream().collect(Collectors.toMap(BpmBusinessRelationDO::getProcessInstanceId, Function.identity(), (old, curr) -> curr));

        List<BpmBusinessRelationRespVO2> targetList = bpmProcessInstanceRespVOList.stream().map(bpmProcessInstanceRespVO -> {
            BpmBusinessRelationDO bpmBusinessRelationDO = businessRelationDOMap.get(bpmProcessInstanceRespVO.getId());

            return BeanUtils.toBean(bpmProcessInstanceRespVO, BpmBusinessRelationRespVO2.class)
                .setBusinessRelation(BeanUtils.toBean(bpmBusinessRelationDO, BpmBusinessRelationRespVO.class));
        }).toList();

        return new PageResult<>(targetList, pageResult.getTotal());
    }


    @Override
    public List<BpmProcessInstanceRespVO> buildProcessInstanceList(Set<String> processInstanceIdList) {
        // 查询历史流程实例（状态在流程变量中，需要带includeProcessVariables的查询）
        List<HistoricProcessInstance> historicProcessInstances = historyService.createHistoricProcessInstanceQuery()
            .processInstanceIds(processInstanceIdList).includeProcessVariables().list();

        // 拼接返回
        Map<String, List<Task>> taskMap = taskService.getTaskMapByProcessInstanceIds(List.copyOf(processInstanceIdList));
        Map<String, ProcessDefinition> processDefinitionMap = processDefinitionService.getProcessDefinitionMap(
            convertSet(historicProcessInstances, HistoricProcessInstance::getProcessDefinitionId));
        Map<String, BpmCategoryDO> categoryMap = categoryService.getCategoryMap(
            convertSet(processDefinitionMap.values(), ProcessDefinition::getCategory));
        // 发起人信息
        Map<Long, AdminUserRespDTO> userMap = adminUserApi.getUserMap(
            convertSet(historicProcessInstances, processInstance -> NumberUtils.parseLong(processInstance.getStartUserId())));
        Map<Long, DeptRespDTO> deptMap = deptApi.getDeptMap(
            convertSet(userMap.values(), AdminUserRespDTO::getDeptId));

        return BpmProcessInstanceConvert.INSTANCE.buildProcessInstancePage(new PageResult<>(historicProcessInstances, (long) historicProcessInstances.size()),
            processDefinitionMap, categoryMap, taskMap, userMap, deptMap).getList();
    }


    @Override
    public PageResult<BpmApproveRespVO> getBpmBusinessRelationPageWithProductInfo(BpmApprovePageReqVO pageReqVO) {
        // 设置租户id
        if (pageReqVO.getHeadTenantId() == null) {
            TenantDto tenantDto = TenantContextHolder.getTenantContextInfo(TenantConstant.TENANT_CONTEXT_KEY_TENANT_DTO);
            pageReqVO.setHeadTenantId(tenantDto.getHeadTenantId());
        }

        // 设置固定业务类型为首营商品审批
        pageReqVO.setBusinessType(BpmBusinessTypeEnum.PRODUCT_FIRST_APPROVE.code);


        // 分页查询
        Page<BpmBusinessRelationDO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        Page<BpmBusinessRelationDO> pageResult = bpmBusinessRelationMapper.selectPageWithProductInfo(page, pageReqVO);

        List<BpmBusinessRelationDO> list = pageResult.getRecords();
        if (CollUtil.isEmpty(list)) {
            return PageResult.empty(pageResult.getTotal());
        }

        Set<String> processInstanceIdList = convertSet(list, BpmBusinessRelationDO::getProcessInstanceId);
        List<BpmProcessInstanceRespVO> bpmProcessInstanceRespVOList = buildProcessInstanceList(processInstanceIdList);

        // 组装 业务数据 businessRelation
        Map<String, BpmBusinessRelationDO> businessRelationDOMap = list.stream().collect(Collectors.toMap(BpmBusinessRelationDO::getProcessInstanceId, Function.identity(), (old, curr) -> curr));

        List<String> productPrefList = list.stream().map(BpmBusinessRelationDO::getBusinessPref).distinct().toList();
        // 商品信息
        Map<String, ProductInfoDto> productInfoDtoMap =  productInfoService.listProductInfoByPref(productPrefList, true)
            .stream().collect(Collectors.toMap(ProductInfoDto::getPref, Function.identity(), (old, curr) -> curr));

        List<BpmApproveRespVO> targetList = bpmProcessInstanceRespVOList.stream().map(bpmProcessInstanceRespVO -> {
            BpmBusinessRelationDO bpmBusinessRelationDO = businessRelationDOMap.get(bpmProcessInstanceRespVO.getId());

            BpmApproveRespVO target = ProductConvert.INSTANCE.convertProcessInstance2ApproveRespVO(bpmProcessInstanceRespVO);
            BpmBusinessRelationRespVO firstAprBizRlt = ProductConvert.INSTANCE.convertDo2RespVO(bpmBusinessRelationDO);
            target.setBusinessRelation(firstAprBizRlt);
            // 组装商品信息
            ProductInfoRespVO productInfoRespVO = ProductConvert.INSTANCE.convertDto2RespVO(productInfoDtoMap.get(bpmBusinessRelationDO.getBusinessPref()));
            productInfoRespVO.setFirstAprBizRlt(firstAprBizRlt)
                .transformApprovalStatus();
            target.setProductInfo(productInfoRespVO);

            return target;
        }).toList();

        return new PageResult<>(targetList, pageResult.getTotal());
    }

}