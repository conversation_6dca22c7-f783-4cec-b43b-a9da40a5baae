package com.xyy.saas.inquiry.product.server.service.product;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_NOT_EXISTS;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.*;
import static com.xyy.saas.inquiry.product.enums.ProductMixedQueryTypeEnum.*;
import static com.xyy.saas.inquiry.util.PrefUtil.NAME_SUFFIX_PRODUCT_APPEND_UNBUNDLED;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.common.util.validation.ValidationUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xyy.saas.inquiry.annotation.MethodArgumentTrim;
import com.xyy.saas.inquiry.constant.ValidateGroup.Add;
import com.xyy.saas.inquiry.constant.ValidateGroup.Update;
import com.xyy.saas.inquiry.enums.tenant.TenantTypeEnum;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.product.api.bpm.BpmBusinessRelationDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductFlag;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoUpdateDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoUpdateDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductUseInfoDto;
import com.xyy.saas.inquiry.product.api.product.dto.qualification.ProductQualificationInfoDto;
import com.xyy.saas.inquiry.product.api.product.dto.qualification.ProductQualificationInfoDto.ProductQualificationInfo;
import com.xyy.saas.inquiry.product.enums.BpmBusinessTypeEnum;
import com.xyy.saas.inquiry.product.enums.ErrorCodeConstants;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.enums.ProductMixedQueryTypeEnum;
import com.xyy.saas.inquiry.product.enums.ProductStatusEnum;
import com.xyy.saas.inquiry.product.enums.ProductStdlibStatusEnum;
import com.xyy.saas.inquiry.product.enums.ProductTransferStatusEnum;
import com.xyy.saas.inquiry.product.server.config.properties.ProductInfoProperties;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmBusinessRelationPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmBusinessRelationRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductFlagUpdateReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductInfoPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductInfoRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductPriceUpdateReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductUnbundledBatchSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductUnbundledSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.product.vo.ProductPresentPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.product.vo.ProductPresentSaveReqVO;
import com.xyy.saas.inquiry.product.server.convert.product.ProductConvert;
import com.xyy.saas.inquiry.product.server.dal.dataobject.bpm.BpmBusinessRelationDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductInfoDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductQualificationInfoDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductUseInfoDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.transfer.ProductTransferRecordDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductInfoDO2;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductInfoMapper;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductQualificationInfoMapper;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductUseInfoMapper;
import com.xyy.saas.inquiry.product.server.mq.producer.mid.MidStdlibInteractiveProducer;
import com.xyy.saas.inquiry.product.server.service.bpm.BpmBusinessRelationService;
import com.xyy.saas.inquiry.product.server.service.gsp.ProductPriceAdjustmentRecordService;
import com.xyy.saas.inquiry.product.server.service.gsp.ProductQualityChangeRecordService;
import com.xyy.saas.inquiry.product.server.service.transfer.ProductTransferRecordService;
import com.xyy.saas.inquiry.util.PrefUtil;
import com.xyy.saas.inquiry.util.UserUtil;
import com.xyy.saas.inquiry.util.UserUtil.UserStringIdFiller;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import jakarta.validation.Validator;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 商品基本信息 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
@ConditionalOnProperty(
    name = "product.service.refactored.enabled",
    havingValue = "false",
    matchIfMissing = true  // 默认启用原有实现
)
public class ProductInfoServiceImpl implements ProductInfoService {

    @Resource
    private Validator validator;

    @Resource
    private ProductInfoMapper productInfoMapper;
    @Resource
    private ProductQualificationInfoMapper productQualificationInfoMapper;
    @Resource
    private ProductUseInfoMapper productUseInfoMapper;

    @Resource
    private ProductStdlibService stdlibService;

    @Resource
    private ProductQualityChangeRecordService qualityChangeRecordService;
    @Resource
    private ProductPriceAdjustmentRecordService priceAdjustmentRecordService;

    @Resource
    private BpmBusinessRelationService bpmBusinessRelationService;
    @Resource
    private ProductTransferRecordService transferRecordService;

    @Resource
    private MidStdlibInteractiveProducer midStdlibInteractiveProducer;

    @Resource
    private TenantApi tenantApi;
    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private ProductInfoProperties productInfoProperties;

    @Resource
    @Lazy
    private ProductInfoService selfProxy;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdateProduct(@MethodArgumentTrim @Nonnull ProductInfoDto dto, ProductBizTypeEnum bizType) {
        // 1. 组装租户信息
        assembleTenantInfo(dto);
        // 如果bizType不传，根据参数判断业务类型
        bizType = ProductBizTypeEnum.checkIfNullBizType(dto, bizType);

        // 2. 组装商品资质+使用信息（需要在处理标准库之前查询数据，否则原数据可能会变更）
        ProductInfoDto origin = getProductInfo(dto.getId(), dto.getTenant());
        if (dto.getId() != null && origin == null) {
            throw exception(PRODUCT_INFO_NOT_EXISTS);
        }
        // 禁采状态转换
        if (dto.getPurchaseDisabled() != null) {
            dto.setProductFlag(Optional.ofNullable(dto.getProductFlag()).orElseGet(ProductFlag::new).mappingPurchaseDisabled(dto.getPurchaseDisabled()));
        }

        // 3. 处理商品状态和属性标志
        bizType.handleProductStatusOrFlag(dto);

        // 4. 处理商品标准库
        if (!bizType.ignoreHandleStdlib()) {
            stdlibService.saveOrUpdateStdlib(dto, true);
        }

        // 4.1.校验商品标准库id是否重复
        validateProductStdlibUnique(dto, bizType.throwExceptionIfStdlibUnique());
        // 如果标准库id重复，改为修改数据，则origin需要重新查询
        origin = getProductInfo(dto.getId(), dto.getTenant());

        // 5. 保存或更新商品信息
        boolean isCreate = dto.getId() == null;
        ProductInfoDO productInfo = saveOrUpdateProductInfoWithGsp(dto, bizType, origin);

        // 5.1.批量插入资质信息
        saveOrUpdateQualificationInfo(dto);
        // 5.2.批量插入使用信息
        saveOrUpdateUseInfo(dto);

        // 6.创建审批流
        BpmBusinessTypeEnum businessTypeEnum = BpmBusinessTypeEnum.getByAuditingStatus(dto.getStatus());
        if (businessTypeEnum != null) {
            Long loginUserId = WebFrameworkUtils.getLoginUserId();
            if (loginUserId == null) {
                throw exception(USER_NOT_EXISTS);
            } else {
                bpmBusinessRelationService.createProcessInstance(loginUserId, new BpmBusinessRelationDto()
                    .setBusinessPref(productInfo.getPref())
                    .setBusinessType(businessTypeEnum.code)
                    .setTenantId(dto.getTenantId())
                    .setHeadTenantId(dto.getHeadTenantId())
                    .setApplicant("" + loginUserId)
                );
            }
        }

        // 7. 新建标品 - 直接调用 （不调用匹配接口，直接上报中台）
        if ((isCreate && bizType == ProductBizTypeEnum.MID_STDLIB_ADD)) {
            stdlibService.reportProduct2MidStdlib(dto);
        }

        return productInfo.getId();
    }

    /**
     * 组装租户信息
     * @param dto
     */
    private void assembleTenantInfo(ProductInfoDto dto) {
        if (dto.getTenantId() == null) {
            dto.setTenant(tenantApi.getTenant());
        } else {
            dto.setTenant(tenantApi.getTenant(dto.getTenantId()));
        }
        if (dto.getHeadTenantId() == null) {
            dto.setHeadTenantId(dto.getTenantId());
        }
    }

    /**
     * 保存或更新商品信息, 并且保存gsp信息（售价调整，质量变更）
     * @param dto
     * @return
     */
    private @NotNull ProductInfoDO saveOrUpdateProductInfoWithGsp(@Nonnull ProductInfoDto dto, @Nonnull ProductBizTypeEnum bizTypeEnum, @Nullable ProductInfoDto origin) {
        if (ObjectUtil.isNull(origin)) {
            ProductInfoDO productInfo = assembleCreateProductInfo(dto, bizTypeEnum);
            productInfoMapper.insert(productInfo);
            dto.setId(productInfo.getId())
                .setPref(productInfo.getPref())
                .setShowPref(productInfo.getShowPref())
                .setMnemonicCode(productInfo.getMnemonicCode());
            return productInfo;
        }

        dto.setPref(origin.getPref())
            .setShowPref(origin.getShowPref())
            .setMnemonicCode(origin.getMnemonicCode());
        // 修改售价（零售价 ｜ 会员价），需要增加售价调整单（不走审批流）
        // 修改其他字段，需要增加质量变更记录（不走审批流）
        qualityChangeRecordService.saveQualityChangeRecord(dto, origin);
        priceAdjustmentRecordService.savePriceAdjustmentRecord(dto, origin, List.of(dto.getTenantId()));

        // 更新商品信息（顺序不能变）
        ProductInfoDO productInfo = assembleUpdateProductInfo(dto, origin);
        productInfoMapper.updateById(productInfo);
        return productInfo;
    }

    /**
     * 组装创建商品模型
     * @param dto
     * @return
     */
    private ProductInfoDO assembleCreateProductInfo(@Nonnull ProductInfoDto dto, @Nonnull ProductBizTypeEnum bizTypeEnum) {
        ProductInfoDO productInfo = BeanUtils.toBean(dto, ProductInfoDO.class);
        // 设置租户id
        productInfo.setTenantId(ObjectUtil.defaultIfNull(dto.getHeadTenantId(), dto.getTenantId()));

        // 根据id 判断是否存在
        if (StringUtils.isBlank(dto.getShowPref())) {
            // 商品编码如果为空，则系统自动生成，这里查询会会查出全部的数据（包含逻辑删除的）
            Supplier<List<String>> initAllPrefSp = () -> productInfoMapper.listShowPref(dto.getHeadTenantId());

            // 拆零商品外码：源商品外码+CL
            String showPref = bizTypeEnum == ProductBizTypeEnum.UNBUNDLED_PRODUCT
                ? PrefUtil.getUnbundledProductShortPref(dto.getHeadTenantId(), dto.getSourceProductShowPref(), initAllPrefSp)
                : PrefUtil.getProductShortPref(dto.getHeadTenantId(), initAllPrefSp);

            productInfo.setShowPref(showPref);
        }
        // 校验 租户 + 商品编号是否唯一
        validateProductShowPrefUnique(productInfo);

        productInfo.setPref(PrefUtil.getProductPref());
        // 商品助记码如果为空，则系统自动生成
        productInfo.calcMnemonicCode();

        // 多属性转换为数值
        long multiFlag = ProductFlag.toFlag(null, dto.getProductFlag());
        productInfo.setMultiFlag(multiFlag);

        return productInfo;
    }

    /**
     * 组装更新商品模型
     * @param dto
     * @param origin
     * @return
     */
    private ProductInfoDO assembleUpdateProductInfo(ProductInfoDto dto, ProductInfoDto origin) {
        ProductInfoDO productInfo = BeanUtils.toBean(dto, ProductInfoDO.class);

        // 租户 + 商品编号 不允许变更
        productInfo.setTenantId(origin.getTenantId());
        productInfo.setShowPref(origin.getShowPref());

        // 多属性转换为数值: 非新增场景：需要合并原有多属性
        long multiFlag = ProductFlag.toFlag(origin.getMultiFlag(), dto.getProductFlag());

        return productInfo.setMultiFlag(multiFlag);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProductInfo(ProductInfoUpdateDto dto) {
        if (dto == null || CollectionUtils.isEmpty(dto.getIdList())) {
            return;
        }

        // 更新商品信息
        productInfoMapper.batchUpdate(dto);
    }

    /**
     * 批量修改 商品属性标志
     *
     * @param updateReqVO 修改信息
     * @return 编号列表
     */
    @Override
    @Transactional
    public List<Long> updateProductFlag(ProductFlagUpdateReqVO updateReqVO) {
        if (updateReqVO == null || CollectionUtils.isEmpty(updateReqVO.getIds())) {
            return List.of();
        }
        if (updateReqVO.getPurchaseDisabled() == null && updateReqVO.getStopSale() == null && updateReqVO.getSpecialPrice() == null && updateReqVO.getIntegral() == null) {
            return List.of();
        }

        ProductFlag updateFlag = updateReqVO.mappingProductFlag();
        // bit运算更新，需要先查询数据，设置multiFlag
        List<ProductInfoDO> updateDOList = productInfoMapper.selectBatchIds(updateReqVO.getIds()).stream().map(p ->
            new ProductInfoDO().setId(p.getId()).setMultiFlag(ProductFlag.toFlag(p.getMultiFlag(), updateFlag))).toList();

        if (CollectionUtils.isNotEmpty(updateDOList)) {
            productInfoMapper.updateBatch(updateDOList);
        }

        return updateDOList.stream().map(ProductInfoDO::getId).toList();
    }

    /**
     * 修改商品价格（支持连锁场景）
     *
     * @param updateReqVO 价格修改信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProductPrice(ProductPriceUpdateReqVO updateReqVO) {
        // 1. 校验商品是否存在
        ProductInfoDO productInfo = productInfoMapper.selectById(updateReqVO.getId());
        if (productInfo == null) {
            throw exception(PRODUCT_INFO_NOT_EXISTS);
        }
        TenantDto tenant = tenantApi.getTenant(productInfo.getTenantId());

        // 获取所有相关租户
        Set<Long> allTenantIdSet = new HashSet<>();
        allTenantIdSet.add(tenant.getId());
        // 获取连锁门店
        allTenantIdSet.addAll(tenantApi.getTenantIdsByHeadId());

        // 2. 确定目标租户列表
        List<Long> targetTenantIds = updateReqVO.getTargetTenantIdList();
        if (CollectionUtils.isEmpty(targetTenantIds)) {
            targetTenantIds = List.of(TenantContextHolder.getRequiredTenantId());
        }

        // 3. 校验目标租户是否合法
        if (!allTenantIdSet.containsAll(targetTenantIds)) {
            throw exception(ErrorCodeConstants.PRODUCT_PRICE_UPDATE_TENANT_NOT_MATCH);
        }

        // 3. 批量查询各租户的商品使用信息
        List<ProductUseInfoDO> existingUseInfoList = productUseInfoMapper.selectList(
            targetTenantIds, List.of(productInfo.getPref()));

        // 4. 构建租户使用信息映射（租户ID -> 使用信息）
        Map<Long, ProductUseInfoDO> tenantUseInfoMap = existingUseInfoList.stream()
            .collect(Collectors.toMap(ProductUseInfoDO::getTenantId, Function.identity()));

        // 5. 为每个目标租户处理价格更新
        List<ProductUseInfoDO> toUpdateList = new ArrayList<>();

        for (Long tenantId : targetTenantIds) {
            ProductUseInfoDO existingUseInfo = tenantUseInfoMap.get(tenantId);

            // 5.1 如果租户没有使用信息，创建默认记录
            if (existingUseInfo == null) {
                existingUseInfo = createDefaultUseInfo(productInfo, tenantId);
            }

            // 5.2 构建更新后的使用信息
            ProductUseInfoDO updatedUseInfo = buildUpdatedUseInfo(existingUseInfo, updateReqVO);
            toUpdateList.add(updatedUseInfo);

            // 5.3 构建价格变更记录（用于售价调整单）
            ProductInfoDto originalRecord = buildPriceChangeRecord(productInfo, existingUseInfo, tenantId);
            ProductInfoDto updatedRecord = buildPriceChangeRecord(productInfo, updatedUseInfo, tenantId);

            // 5.4 生成售价调整单记录
            priceAdjustmentRecordService.savePriceAdjustmentRecord(
                updatedRecord, originalRecord, List.of(tenantId));
        }

        // 6. 批量更新商品使用信息
        batchSaveOrUpdateUseInfo(toUpdateList);
    }

    /**
     * 创建默认商品使用信息
     */
    private ProductUseInfoDO createDefaultUseInfo(ProductInfoDO productInfo, Long tenantId) {
        ProductUseInfoDO useInfo = ProductUseInfoDO.builder()
            .productPref(productInfo.getPref())
            .headTenantId(productInfo.getTenantId())
            .build();
        useInfo.setTenantId(tenantId);
        return useInfo;
    }

    /**
     * 构建更新后的使用信息
     */
    private ProductUseInfoDO buildUpdatedUseInfo(ProductUseInfoDO existing, ProductPriceUpdateReqVO updateReqVO) {
        ProductUseInfoDO updated = new ProductUseInfoDO();
        BeanUtils.copyProperties(existing, updated);

        // 只更新传入的价格字段
        if (updateReqVO.getRetailPrice() != null) {
            updated.setRetailPrice(updateReqVO.getRetailPrice());
        }
        if (updateReqVO.getMemberPrice() != null) {
            updated.setMemberPrice(updateReqVO.getMemberPrice());
        }

        return updated;
    }

    /**
     * 构建价格变更记录
     */
    private ProductInfoDto buildPriceChangeRecord(ProductInfoDO productInfo, ProductUseInfoDO useInfo, Long tenantId) {
        ProductInfoDto record = new ProductInfoDto();
        record.setId(productInfo.getId());
        record.setPref(productInfo.getPref());
        record.setTenantId(tenantId);
        record.setHeadTenantId(productInfo.getTenantId());

        // 设置使用信息
        ProductUseInfoDto useInfoDto = BeanUtils.toBean(useInfo, ProductUseInfoDto.class);
        record.setUseInfo(useInfoDto);

        return record;
    }

    /**
     * 批量保存或更新使用信息
     */
    private void batchSaveOrUpdateUseInfo(List<ProductUseInfoDO> useInfoList) {
        for (ProductUseInfoDO useInfo : useInfoList) {
            if (useInfo.getId() == null) {
                productUseInfoMapper.insert(useInfo);
            } else {
                productUseInfoMapper.updateById(useInfo);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProductInfo(Long id) {
        // 校验存在
        ProductInfoDO exist = validateProductInfoExists(id);
        // 状态为使用中
        if (!Objects.equals(ProductStatusEnum.USING.code, exist.getStatus())) {
            throw exception(PRODUCT_INFO_CAN_NOT_DELETE, exist.getShowPref());
        }

        // 删除
        productInfoMapper.deleteById(id);

        // 删除资质信息
        productQualificationInfoMapper.deleteByProductPref(exist.getPref());
        // 删除使用信息
        productUseInfoMapper.deleteByProductPref(exist.getPref());
    }

    private ProductInfoDO validateProductInfoExists(Long id) {
        ProductInfoDO productInfoDO = productInfoMapper.selectById(id);
        if (productInfoDO == null) {
            throw exception(PRODUCT_INFO_NOT_EXISTS);
        }
        return productInfoDO;
    }

    private ProductInfoDO validateSourceProductExists(String sourcePref) {
        return Optional.ofNullable(productInfoMapper.selectOne(ProductInfoDO::getPref, sourcePref))
            .orElseThrow(() -> exception(PRODUCT_INFO_NOT_EXISTS));
    }

    /**
     * 批量校验源商品存在性、
     */
    private Map<String, ProductInfoDO> validateSourceProductsExistInBatch(Set<String> sourceProductPrefSet) {
        List<ProductInfoDO> sourceProducts = productInfoMapper.listByPref(new ArrayList<>(sourceProductPrefSet));

        Map<String, ProductInfoDO> sourceProductMap = sourceProducts.stream()
            .filter(product -> {
                // 限制：源商品不能是拆零商品
                if (ProductFlag.of(product.getMultiFlag()).getUnbundled()) {
                    throw exception(PRODUCT_UNBUNDLED_NESTED, product.getCommonName());
                }
                return true;
            })
            .collect(Collectors.toMap(ProductInfoDO::getPref, Function.identity()));

        // 检查是否有源商品不存在
        Set<String> existingPrefs = sourceProductMap.keySet();
        Set<String> missingPrefs = sourceProductPrefSet.stream()
            .filter(pref -> !existingPrefs.contains(pref))
            .collect(Collectors.toSet());

        if (!missingPrefs.isEmpty()) {
            throw exception(PRODUCT_INFO_NOT_EXISTS, String.join(", ", missingPrefs));
        }

        return sourceProductMap;
    }

    /**
     * 批量校验拆零商品的拆零数量唯一性
     * @param unbundledRequests 拆零商品请求列表
     * @param sourceProductMap 源商品信息映射
     */
    private void validateUnbundledQuantityUniqueInBatch(List<ProductUnbundledSaveReqVO> unbundledRequests,
                                                       Map<String, ProductInfoDO> sourceProductMap) {
        // 收集所有源商品编码
        Set<String> sourceProductPrefSet = unbundledRequests.stream()
            .map(ProductUnbundledSaveReqVO::getSourceProductPref)
            .collect(Collectors.toSet());

        // 批量查询已存在的拆零商品
        List<ProductInfoDO> existingUnbundledProducts = productInfoMapper
            .findUnbundledProductsBySourceProducts(new ArrayList<>(sourceProductPrefSet));

        // 按源商品编码分组已存在的拆零商品
        Map<String, List<ProductInfoDO>> existingProductsMap = existingUnbundledProducts.stream()
            .collect(Collectors.groupingBy(ProductInfoDO::getSourceProductPref));

        // 校验每个新增请求
        for (ProductUnbundledSaveReqVO request : unbundledRequests) {
            String sourceProductPref = request.getSourceProductPref();
            Integer newUnbundledQuantity = request.getUnbundledQuantity();

            // 获取该源商品下已存在的拆零商品
            List<ProductInfoDO> existingProducts = existingProductsMap.getOrDefault(sourceProductPref, new ArrayList<>());

            // 检查是否存在相同拆零数量
            boolean isDuplicate = existingProducts.stream()
                .anyMatch(product -> Objects.equals(product.getUnbundledQuantity(), newUnbundledQuantity));

            if (isDuplicate) {
                ProductInfoDO sourceProduct = sourceProductMap.get(sourceProductPref);
                String sourceProductName = sourceProduct != null ? sourceProduct.getCommonName() : sourceProductPref;
                throw exception(PRODUCT_UNBUNDLED_QUANTITY_DUPLICATED, sourceProductName, newUnbundledQuantity);
            }
        }

        // 校验当前批次内部是否有重复的拆零数量
        validateInternalDuplicates(unbundledRequests, sourceProductMap);
    }

    /**
     * 校验当前批次内部是否有重复的源商品+拆零数量组合
     */
    private void validateInternalDuplicates(List<ProductUnbundledSaveReqVO> unbundledRequests,
                                           Map<String, ProductInfoDO> sourceProductMap) {
        Map<String, Set<Integer>> batchUnbundledMap = new HashMap<>();

        for (ProductUnbundledSaveReqVO request : unbundledRequests) {
            String sourceProductPref = request.getSourceProductPref();
            Integer unbundledQuantity = request.getUnbundledQuantity();

            Set<Integer> quantities = batchUnbundledMap.computeIfAbsent(sourceProductPref, k -> new HashSet<>());

            if (!quantities.add(unbundledQuantity)) {
                // 当前批次内存在重复
                ProductInfoDO sourceProduct = sourceProductMap.get(sourceProductPref);
                String sourceProductName = sourceProduct != null ? sourceProduct.getCommonName() : sourceProductPref;
                throw exception(PRODUCT_UNBUNDLED_QUANTITY_DUPLICATED, sourceProductName, unbundledQuantity);
            }
        }
    }

    private void validateProductShowPrefUnique(ProductInfoDO productInfo) {
        if (productInfo == null) {
            return;
        }
        // 机构+外码唯一性校验（忽略当前id）
        ProductInfoDO exists = productInfoMapper.uniqueIndexExists(productInfo.getTenantId(), productInfo.getShowPref(), null);
        if (exists != null && !Objects.equals(productInfo.getId(), exists.getId())) {
            throw exception(PRODUCT_INFO_DUPLICATE_SHOW_PREF, productInfo.getShowPref(), exists.tips4Deleted());
        }
    }

    private void validateProductStdlibUnique(ProductInfoDto dto, boolean exceptionIfExists) {
        // 拆零品 stdlib id为空，则不校验
        if (dto == null || dto.getStdlibId() == null) {
            return;
        }
        // 机构+标准库id唯一性校验（忽略当前id）
        ProductInfoDO exists = productInfoMapper.uniqueIndexExists(dto.getHeadTenantId(), null, dto.getStdlibId());
        if (exists == null || Objects.equals(dto.getId(), exists.getId())) {
            return;
        }
        // 存在则抛异常
        if (exceptionIfExists) {
            throw exception(PRODUCT_INFO_DUPLICATE_STDLIB, exists.getShowPref(), exists.tips4Deleted());
        }
        // 否则由新增变成修改
        dto.setId(exists.getId());
    }

    @Override
    public ProductInfoDto getProductInfo(Long id, boolean getCurrentTenantInfo) {
        return getProductInfo(id, getCurrentTenantInfo ? tenantApi.getTenant() : null);
    }

    @Override
    public ProductInfoDto getProductInfo(Long id, TenantDto tenant) {
        if (id == null) {
            return null;
        }
        List<ProductInfoDO2> productInfoDO2List = productInfoMapper.listByIdOrPref(List.of(id), null, tenant, false);
        if (CollectionUtils.isEmpty(productInfoDO2List)) {
            return null;
        }
        List<ProductInfoDto> dtoList = convertListDoToDto(tenant.getId(), tenant.getHeadTenantId(), productInfoDO2List);
        return dtoList.getFirst();
    }

    @Override
    public List<ProductInfoDto> listProductInfoByPref(List<String> prefList, boolean getCurrentTenantInfo) {
        return listProductInfoByPref(prefList, getCurrentTenantInfo ? tenantApi.getTenant() : null);
    }

    @Override
    public List<ProductInfoDto> listProductInfoByPref(List<String> prefList, TenantDto tenant) {
        if (CollectionUtils.isEmpty(prefList)) {
            return List.of();
        }

        List<ProductInfoDO2> productInfoDO2List = productInfoMapper.listByIdOrPref(null, prefList, tenant, false);

        List<ProductInfoDto> result = new ArrayList<>();
        // 按照 tenantId 分组，然后组装数据
        productInfoDO2List.stream().collect(Collectors.groupingBy(ProductInfoDO2::getTenantId)).forEach((tenantId, list) -> {
            // 转换分页数据
            List<ProductInfoDto> dtoList = convertListDoToDto(tenant != null ? tenant.getId() : tenantId, tenantId, list);
            result.addAll(dtoList);
        });

        return result;
    }

    @Override
    public void validTenantProductCountLimit(Long tenantId, long addCount) {
        TenantDto tenantDto = tenantApi.getTenant(tenantId);
        long limit = tenantDto.getType().isChain() ? productInfoProperties.getChainProductCountLimit() : productInfoProperties.getStoreProductCountLimit();
        long tenantProductCount = productInfoMapper.getTenantProductCount(tenantId);

        if (tenantProductCount + addCount > limit) {
            throw exception(PRODUCT_INFO_COUNT_LIMIT, limit);
        }
    }

    @Override
    public PageResult<ProductInfoDto> getProductInfoPage(ProductInfoPageReqVO pageReqVO) {
        if (pageReqVO.getTenantId() == null) {
            TenantDto tenant = tenantApi.getTenant();
            pageReqVO.setTenantId(tenant.getId());
            pageReqVO.setHeadTenantId(tenant.getHeadTenantId());
        }
        if (pageReqVO.getHeadTenantId() == null) {
            pageReqVO.setHeadTenantId(pageReqVO.getTenantId());
        }

        // 过滤问诊临时建品
        pageReqVO.excludeTempStatus();

        // 转换混合查询关键字
        pageReqVO.transformMixedQuery();
        // 转换商品标志
        pageReqVO.transformProductFlag();

        Page<ProductInfoDO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        // 查询分页
        Page<ProductInfoDO2> selectedPage = productInfoMapper.selectPageByParam(page, pageReqVO);
        // 转换分页数据
        List<ProductInfoDto> dtoList = convertListDoToDto(pageReqVO.getTenantId(), pageReqVO.getHeadTenantId(), selectedPage.getRecords());

        return new PageResult<>(dtoList, selectedPage.getTotal());
    }

    @Override
    public PageResult<ProductInfoRespVO> getProductInfoWithBizRltPage(ProductInfoPageReqVO pageReqVO) {
        PageResult<ProductInfoDto> page = getProductInfoPage(pageReqVO);
        List<ProductInfoDto> dtoList = page.getList();

        // 组装审批流程数据
        List<String> productPrefList = dtoList.stream().map(ProductInfoDto::getPref).toList();
        // 查询商品关联的首营审批、总部审批记录（id降序，取最新的数据即可）
        BpmBusinessRelationPageReqVO bizRltPageReqVO = new BpmBusinessRelationPageReqVO()
            .setBusinessTypeList(List.of(BpmBusinessTypeEnum.PRODUCT_FIRST_APPROVE.code, BpmBusinessTypeEnum.PRODUCT_TO_HEADQUARTERS_APPROVE.code))
            .setBusinessPrefList(productPrefList);

        PageResult<BpmBusinessRelationDO> bpmBusinessRelationPage = bpmBusinessRelationService.getBpmBusinessRelationPage(bizRltPageReqVO);
        // 按照 businessPref, businessType 分组，不覆盖数据（保证顺序 - 最前面的就是最新的数据）
        Map<String, Map<Integer, BpmBusinessRelationDO>> bizRltGroupMap = bpmBusinessRelationPage.getList().stream().collect(
            Collectors.groupingBy(BpmBusinessRelationDO::getBusinessPref,
                LinkedHashMap::new,
                Collectors.toMap(BpmBusinessRelationDO::getBusinessType, Function.identity(), (v1, v2) -> v1, LinkedHashMap::new))
        );

        List<ProductInfoRespVO> respVOList = dtoList.stream().map(i -> {
            ProductInfoRespVO respVO = ProductConvert.INSTANCE.convertDto2RespVO(i);
            // 转换审批状态
            respVO.transformApprovalStatus();

            Optional.ofNullable(bizRltGroupMap.get(i.getPref())).ifPresent(m -> {
                // 组装首营审批流程
                BpmBusinessRelationDO firstAprBizRlt = m.get(BpmBusinessTypeEnum.PRODUCT_FIRST_APPROVE.code);
                respVO.setFirstAprBizRlt(ProductConvert.INSTANCE.convertDo2RespVO(firstAprBizRlt));
                // 组装总部审批流程
                BpmBusinessRelationDO headAprBizRlt = m.get(BpmBusinessTypeEnum.PRODUCT_TO_HEADQUARTERS_APPROVE.code);
                respVO.setHeadAprBizRlt(ProductConvert.INSTANCE.convertDo2RespVO(headAprBizRlt));
            });

            return respVO;
        }).toList();

        return new PageResult<>(respVOList, page.getTotal());
    }

    private @Nonnull List<ProductInfoDto> convertListDoToDto(Long tenantId, Long headTenantId, List<ProductInfoDO2> records) {
        List<String> prefList = records.stream().map(ProductInfoDO2::getPref).toList();

        // 如果有总部租户，则查询总部的商品使用信息，上面分页查询的是门店使用信息
        if (!Objects.equals(headTenantId, tenantId)) {
            Map<String, ProductUseInfoDO> headUseInfoMap = productUseInfoMapper.selectMapList(prefList, headTenantId, headTenantId);
            // 组装总部使用信息
            records.forEach(i -> {
                // 门店有使用信息， 不覆盖
                if (i.getUseInfo() != null && i.getUseInfo().getProductPref() != null) {
                    return;
                }
                ProductUseInfoDO headUseInfo = headUseInfoMap.get(i.getPref());
                if (headUseInfo != null) {
                    i.setUseInfo(headUseInfo);
                }
            });
        }

        // 查询资质信息
        Map<String, List<ProductQualificationInfo>> qualificationGroupByProductPref = productQualificationInfoMapper.selectList(prefList)
            .stream().map(i -> BeanUtil.toBean(i, ProductQualificationInfo.class))
            .collect(Collectors.groupingBy(ProductQualificationInfo::getProductPref));

        // 拆零商品查询源商品信息（不支持多层级嵌套拆零）
        List<String> sourceProductPrefList = records.stream().map(ProductInfoDO2::getSourceProductPref).filter(Objects::nonNull).toList();
        Map<String, ProductInfoDO2> sourceProductMap = productInfoMapper.listByIdOrPref(null, sourceProductPrefList, null, false)
            .stream().collect(Collectors.toMap(ProductInfoDO2::getPref, Function.identity(), (a, b) -> b));

        List<ProductInfoDto> dtoList = records.stream().map(infoDO2 -> {
            // 组装源商品信息, 在convert之前
            Optional.ofNullable(infoDO2.getSourceProductPref()).flatMap(i -> Optional.ofNullable(sourceProductMap.get(i))).ifPresent(sourceProduct -> {
                infoDO2.setStdlib(sourceProduct.getStdlib());
            });

            ProductInfoDto dto = ProductConvert.INSTANCE.convertProductInfoDO2ToDto(infoDO2);

            // 组装使用信息
            dto.setUseInfo(ProductConvert.INSTANCE.convertDo2Dto(infoDO2.getUseInfo()));
            // 组装资质信息
            List<ProductQualificationInfo> infoList = qualificationGroupByProductPref.get(infoDO2.getPref());
            dto.setQualificationInfo(ProductQualificationInfoDto.of(infoList));

            return dto;
        }).toList();

        // 用户信息
        UserUtil.fillUserInfo2(dtoList, adminUserApi::getUserNameMap,
            new UserStringIdFiller<>(ProductInfoDto::getCreator, ProductInfoDto::setCreatorName),
            new UserStringIdFiller<>(ProductInfoDto::getUpdater, ProductInfoDto::setUpdaterName) );

        return dtoList;
    }

    /**
     * 保存或更新资质信息
     * @param dto
     */
    private void saveOrUpdateQualificationInfo(ProductInfoDto dto) {
        if (dto == null || dto.getId() == null || dto.getQualificationInfo() == null) {
            return;
        }
        // 批量插入资质信息
        ProductQualificationInfoDto qualificationInfo = dto.getQualificationInfo();
        qualificationInfo.setProductPref(dto.getPref());
        List<ProductQualificationInfoDO> qualificationInfoDOList = BeanUtil.copyToList(qualificationInfo.toInfoList(), ProductQualificationInfoDO.class);
        if (qualificationInfoDOList.isEmpty()) {
            return;
        }
        productQualificationInfoMapper.insertOrUpdateOnDuplicate(qualificationInfoDOList);
    }

    /**
     * 保存或更新使用信息
     * @param dto
     */
    private void saveOrUpdateUseInfo(ProductInfoDto dto) {
        if (dto == null || dto.getId() == null || dto.getUseInfo() == null) {
            return;
        }
        // 批量插入使用信息
        ProductUseInfoDto useInfo = dto.getUseInfo();
        useInfo.setProductPref(dto.getPref());
        useInfo.setTenantId(dto.getTenantId());
        useInfo.setHeadTenantId(dto.getHeadTenantId());

        // 查询修改前的数据
        ProductUseInfoDO before = productUseInfoMapper.selectOne(new LambdaQueryWrapperX<ProductUseInfoDO>()
            .eq(ProductUseInfoDO::getProductPref, dto.getPref())
            .eq(ProductUseInfoDO::getTenantId, dto.getTenantId())
        );

        ProductUseInfoDO useInfoDO = BeanUtils.toBean(useInfo, ProductUseInfoDO.class);
        // 记录上一次数据（可恢复）
        useInfoDO.setRedoData(useInfoDO.compareBeforeToRedo(before));
        if (before != null) {
            useInfoDO.setId(before.getId());
        }

        productUseInfoMapper.insertOrUpdate(useInfoDO);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> saveOrUpdateUnbundledProduct(ProductUnbundledBatchSaveReqVO batchSaveReqVO) {
        if (batchSaveReqVO == null || CollectionUtils.isEmpty(batchSaveReqVO.getProducts())) {
            return List.of();
        }

        List<ProductUnbundledSaveReqVO> products = batchSaveReqVO.getProducts();

        // 校验商品数量限制（按新增处理）
        validTenantProductCountLimit(TenantContextHolder.getRequiredTenantId(), products.size());

        // 批量验证请求参数
        products.forEach(req -> ValidationUtils.validate(validator, req, Add.class));

        // 收集所有源商品编码并批量校验存在性
        Set<String> sourceProductPrefSet = products.stream()
            .map(ProductUnbundledSaveReqVO::getSourceProductPref)
            .collect(Collectors.toSet());

        Map<String, ProductInfoDO> sourceProductMap = validateSourceProductsExistInBatch(sourceProductPrefSet);

        // 批量校验拆零数量唯一性
        validateUnbundledQuantityUniqueInBatch(products, sourceProductMap);

        // 批量创建拆零商品
        return products.stream().map(createReqVO -> {
            String sourceProductPref = createReqVO.getSourceProductPref();
            ProductInfoDO sourceProduct = sourceProductMap.get(sourceProductPref);

            // 基于源商品创建拆零商品DTO
            ProductInfoDto dto = BeanUtils.toBean(sourceProduct, ProductInfoDto.class);
            dto.setId(null)
                .setPref(null)
                .setShowPref(null)
                .setCommonName(sourceProduct.getCommonName() + NAME_SUFFIX_PRODUCT_APPEND_UNBUNDLED)
                .setSourceProductPref(sourceProductPref)
                .setSourceProductShowPref(sourceProduct.getShowPref())
                .setStdlibId(null)
                .setMidStdlibId(null)
                .setUnbundledQuantity(createReqVO.getUnbundledQuantity())
                .setSpec(createReqVO.getUnbundledSpec())
                .setUnit(createReqVO.getUnbundledUnit())
                .setRemark(createReqVO.getRemark());

            // 保存拆零商品
            return saveOrUpdateProduct(dto, ProductBizTypeEnum.UNBUNDLED_PRODUCT);
        }).toList();
    }


    // region 保存商品提报
    /**
     * 保存商品提报
     *
     * @param reqVO
     * @return  返回门店商品id
     */
    @Override
    public Long saveProductPresent(ProductPresentSaveReqVO reqVO) {
        // 1. 转换请求参数
        ProductInfoDto dto = BeanUtils.toBean(reqVO, ProductInfoDto.class);
        Long tenantId = TenantContextHolder.getRequiredTenantId();
        dto.setTenantId(tenantId);

        Long transferRecordId = reqVO.getId();
        // 2. 如果是编辑,检查审批状态
        if (transferRecordId != null) {
            ProductTransferRecordDO transferRecord = transferRecordService.getProductTransferRecord(transferRecordId);
            if (transferRecord == null || !Objects.equals(ProductTransferStatusEnum.MID_AUDIT_REJECT.code, transferRecord.getStatus())) {
                throw exception(PRODUCT_PRESENT_CANNOT_EDIT);
            }
            ProductInfoDO productInfo = productInfoMapper.selectOne(ProductInfoDO::getPref, transferRecord.getProductPref());
            // 审批驳回才能编辑
            if (productInfo == null || !Objects.equals(ProductStatusEnum.MID_AUDIT_REJECT.code, productInfo.getStatus())) {
                throw exception(PRODUCT_PRESENT_CANNOT_EDIT);
            }
            // 商品信息组装（这里不能覆盖租户id，否则提报记录可能会变成连锁总部的id）
            dto.setId(productInfo.getId());
        }

        // 3. 查询相同 条形码 的标准库商品（使用中）
        // 条形码传0，不根据条形码查询
        String barcode = reqVO.getBarcode();
        List<ProductStdlibDto> stdlibDtoList = stdlibService.searchStdlibByBarcode(barcode);
        if (CollectionUtils.isNotEmpty(stdlibDtoList)) {
            throw exception(PRODUCT_PRESENT_DUPLICATED_MID_STDLIB);
        }

        // 4. 查询当前门店相同 条形码/六要素 的提报记录（待审批）
        ProductPresentPageReqVO queryReqVO = new ProductPresentPageReqVO()
            .setSourceTenantId(dto.getTenantId())
            .setBarcode(StringUtils.defaultString(barcode));
        if (barcode == null || barcode.equals("0")) {
            queryReqVO.setCommonName(StringUtils.defaultString(reqVO.getCommonName()))
                .setBrandName(StringUtils.defaultString(reqVO.getBrandName()))
                .setSpec(StringUtils.defaultString(reqVO.getSpec()))
                .setManufacturer(StringUtils.defaultString(reqVO.getManufacturer()))
                .setApprovalNumber(StringUtils.defaultString(reqVO.getApprovalNumber()));
        }
        List<ProductTransferRecordDO> transferRecordDOS = transferRecordService.listPresentTransferRecord(queryReqVO);
        if (CollectionUtils.isNotEmpty(transferRecordDOS)) {
            boolean existAuditing = transferRecordDOS.stream().anyMatch(i -> Objects.equals(i.getStatus(), ProductTransferStatusEnum.INIT.code));
            if (existAuditing) {
                throw exception(PRODUCT_PRESENT_DUPLICATED_AUDITING);
            }
            // boolean existAudited = transferRecordDOS.stream().anyMatch(i -> Objects.equals(i.getStatus(), ProductTransferStatusEnum.SUCCESS.code));
            // if (existAudited) {
            //     throw exception(PRODUCT_PRESENT_DUPLICATED_MID_STDLIB);
            // }
        }

        // 5. 查询相同 六要素 的标准库商品（使用中）
        // 排除当前编辑记录，且中台审核驳回后还可以重新提报（提报中台会返回已驳回原因）
        ProductStdlibDO stdlibDO = stdlibService.uniqueQuery(dto);
        if (transferRecordId == null && stdlibDO != null && Objects.equals(stdlibDO.getStatus(), ProductStdlibStatusEnum.USING.code)) {
            throw exception(PRODUCT_PRESENT_DUPLICATED_MID_STDLIB);
        }

        // 6. 保存商品信息（包含上报等操作）
        selfProxy.saveOrUpdateProduct(dto, ProductBizTypeEnum.INQUIRY_PRESENT);

        // 7. 提报商品中台
        ProductTransferRecordDO transferRecordDO = stdlibService.reportProduct2MidStdlib(dto);

        // 8. 重新编辑后新增提报记录id，原提报记录禁用
        if (transferRecordId != null && !Objects.equals(transferRecordId, transferRecordDO.getId())) {
            // 禁用原提报记录
            transferRecordService.disableProductTransferRecord(transferRecordId);
        }
        return transferRecordDO.getId();

    }

    // endregion
}