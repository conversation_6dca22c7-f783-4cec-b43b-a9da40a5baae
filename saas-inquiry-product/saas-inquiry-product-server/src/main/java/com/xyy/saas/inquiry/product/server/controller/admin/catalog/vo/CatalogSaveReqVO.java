package com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo;

import cn.hutool.core.collection.CollUtil;
import com.xyy.saas.inquiry.product.api.catalog.dto.CatalogExtDTO;
import com.xyy.saas.inquiry.product.enums.CatalogTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.MEDICAL_CATALOG_MATCH_IS_NULL;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.MEDICAL_CATALOG_MATCH_RULE_UN_COMPLETED;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PARAM_INVALID;

@Schema(description = "管理后台 - 目录新增/修改 Request VO")
@Data
@Builder
public class CatalogSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5280")
    private Long id;

    @Schema(description = "目录编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String pref;

    @Schema(description = "目录名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "目录名称不能为空")
    private String name;

    @Schema(description = "业务类型（1:医保 3:互联网监管）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "业务类型（1:医保 3:互联网监管）不能为空")
    private Integer type;

    @Schema(description = "项目编码类型（11:医保项目编码 31:自建标准库ID 32:中台标准库ID）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer projectCodeType;

    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @Schema(description = "版本编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String versionCode;

    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED)
    private String province;

    @Schema(description = "省编码")
    @NotEmpty(message = "省编码不能为空")
    private String provinceCode;

    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED)
    private String city;

    @Schema(description = "市编码")
    private String cityCode;

    @Schema(description = "区", requiredMode = Schema.RequiredMode.REQUIRED)
    private String area;

    @Schema(description = "区编码")
    private String areaCode;

    @Schema(description = "上传文件链接", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @NotEmpty(message = "上传文件链接不能为空")
    private String uploadUrl;

    @Schema(description = "是否需要下载", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean needDownload;

    @Schema(description = "下载文件链接", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    private String downloadUrl;

    @Schema(description = "目录总数", requiredMode = Schema.RequiredMode.REQUIRED, example = "15495")
    private Integer totalCount;

    @Schema(description = "已匹配数", requiredMode = Schema.RequiredMode.REQUIRED, example = "14781")
    private Integer matchedCount;

    @Schema(description = "未匹配数", requiredMode = Schema.RequiredMode.REQUIRED, example = "30548")
    private Integer unmatchedCount;

    @Schema(description = "是否禁用，默认否", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否禁用，默认否不能为空")
    private Boolean disable;

    @Schema(description = "环境（1测试、2灰度、3上线）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "环境（1测试、2灰度、3上线）不能为空")
    private Integer env;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    /**
     * 扩展字段（JSON格式）
     */
    @Schema(description = "扩展信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private CatalogExtDTO ext;


    /**
     * 校验扩展信息
     */
    public void validateExt() {
        // 如果是医保目录，则是否匹配标准库 必填
        if (Objects.equals(this.type, CatalogTypeEnum.MEDICAL.getCode()) && (this.ext == null || this.ext.getMatchStdlib() == null)) {
            throw exception(MEDICAL_CATALOG_MATCH_IS_NULL);
        }
        if (this.ext == null || !Boolean.TRUE.equals(this.ext.getMatchStdlib())) {
            return;
        }

        if (CollUtil.isEmpty(this.ext.getWesternMedicineMatchRules())) {
            throw exception(MEDICAL_CATALOG_MATCH_RULE_UN_COMPLETED);
        }
        this.ext.getWesternMedicineMatchRules().forEach(i -> {
            if (StringUtils.isAnyEmpty(i.getSourceField(), i.getOp(), i.getTargetField())) {
                throw exception(MEDICAL_CATALOG_MATCH_RULE_UN_COMPLETED);
            }
        });
        if (CollUtil.isEmpty(this.ext.getChineseMedicineMatchRules())) {
            throw exception(MEDICAL_CATALOG_MATCH_RULE_UN_COMPLETED);
        }
        this.ext.getChineseMedicineMatchRules().forEach(i -> {
            if (StringUtils.isAnyEmpty(i.getSourceField(), i.getOp(), i.getTargetField())) {
                throw exception(MEDICAL_CATALOG_MATCH_RULE_UN_COMPLETED);
            }
        });
    }
}