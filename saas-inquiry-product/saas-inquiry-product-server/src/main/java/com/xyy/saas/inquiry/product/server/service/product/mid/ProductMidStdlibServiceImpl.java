package com.xyy.saas.inquiry.product.server.service.product.mid;

import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.dict.DictDataApi;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.product.api.mid.dto.SaasCategoryDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.server.config.forward.InquiryDubboForwardClient;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidDictionaryVo;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidGeneralMatchProductNewDto;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidGeneralMatchProductVo;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidGeneralProductDto;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidGeneralProductPresentDto;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidGeneralProductPresentInfoVo;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidGeneralProductPresentNewVo;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidGeneralProductVo;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidPictureCondition;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidPictureProResult;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidResponse;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidResponse2;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidResponse3;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidSaasWithMixSearchInfoDto;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidTotalDictionaryReadDto;
import com.xyy.saas.inquiry.product.server.config.forward.dto.Param;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.StdlibProductMixedPageQueryVo;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductInfoMapper;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static java.util.stream.Collectors.toList;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Service
@Validated
@Slf4j
public class ProductMidStdlibServiceImpl implements ProductMidStdlibService {

    @Resource
    private InquiryDubboForwardClient dubboForwardClient;

    @Resource
    private ProductInfoMapper productInfoMapper;

    @Resource
    private DictDataApi dictDataApi;

    // region 中台标准库信息查询（快速录入）
    @Override
    public @Nonnull PageResult<MidSaasWithMixSearchInfoDto> fuzzySearchPlus(StdlibProductMixedPageQueryVo queryVo, boolean isFilterSauAttr) {
        log.info("fuzzySearchPlus 调用中台标准库信息查询参数：{}", JSONUtil.toJsonStr(queryVo));
        MidResponse<MidSaasWithMixSearchInfoDto> resp = dubboForwardClient.fuzzySearchPlus(new Param()
            .typeList("java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "int", "int", "boolean")
            .valueList(queryVo.getProductName(), queryVo.getApprovalNumber(),queryVo.getManufacturer(), queryVo.getMidStdlibId(),
                queryVo.getPageNo(), queryVo.getPageSize(), isFilterSauAttr)
        );
        if (resp == null || resp.getStatus() != 0) {
            log.error("fuzzySearchPlus 调用中台标准库信息查询失败，queryVo:{}, resp: {}", queryVo, resp);
            return PageResult.empty();
        }
        return new PageResult<>(resp.getData(), resp.getCount());
    }

    // endregion


    // region 中台标准库信息查询（根据标准库id列表查询）
    @Override
    public @Nonnull List<MidSaasWithMixSearchInfoDto> getInfoByStandardLibIdList(List<Long> midStdlibIdList) {
        if (CollectionUtils.isEmpty(midStdlibIdList)) {
            return List.of();
        }
        log.info("getInfoByStandardLibIdList 调用中台标准库信息查询参数：midStdlibIdList: {}", JSONUtil.toJsonStr(midStdlibIdList));
        // 1. 分批查询
        return Lists.partition(midStdlibIdList, 500).stream().flatMap(list -> {
            MidResponse<MidSaasWithMixSearchInfoDto> resp = dubboForwardClient.getInfoByStandardLibIdList(new Param()
                .typeList("java.util.List")
                .valueList(midStdlibIdList)
            );
            if (resp == null || resp.getStatus() != 0) {
                log.error("getInfoByStandardLibIdList 调用中台标准库信息查询失败，midStdlibIdList:{}, resp: {}", midStdlibIdList, resp);
                return Stream.empty();
            }
            return Optional.ofNullable(resp.getData()).map(Collection::stream).orElseGet(Stream::empty);
        }).toList();
    }

    // endregion


    // region 中台标准库匹配
    @Override
    public List<MidGeneralMatchProductNewDto> getGeneralMatchProductNew(List<ProductInfoDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return List.of();
        }

        MidGeneralMatchProductVo vo = MidGeneralMatchProductVo.of(dtoList);
        log.info("getGeneralMatchProductNew 调用中台标准库匹配参数：{}", JSONUtil.toJsonStr(vo));

        MidResponse2<List<MidGeneralMatchProductNewDto>> resp = dubboForwardClient.getGeneralMatchProductNew(new Param()
            .typeList("com.xyy.me.product.general.api.vo.product.GeneralMatchProductVo")
            .valueList(vo));
        if (resp == null || resp.isFailure()) {
            log.error("getGeneralMatchProductNew 调用中台标准库匹配失败，resp: {}", JSON.toJSONString(resp));
            return List.of();
        }
        return resp.getData();
    }

    // endregion


    // region 新品上报中台
    @Override
    public MidResponse2<List<MidGeneralProductPresentDto>> addProductPresent(@Nonnull ProductInfoDto dto) {
        MidGeneralProductPresentNewVo vo = MidGeneralProductPresentNewVo.of(dto);
        log.info("addProductPresent 调用中台新品上报参数：{}", JSONUtil.toJsonStr(vo));

        MidResponse2<List<MidGeneralProductPresentDto>> resp = dubboForwardClient.addProductPresent(new Param()
            .typeList("com.xyy.me.product.general.api.newer.vo.present.GeneralProductPresentNewVo")
            .valueList(vo));

        log.info("addProductPresent 新品上报中台 req: {}，resp: {}", JSON.toJSONString(vo), JSON.toJSONString(resp));
        if (resp == null || resp.isFailure()) {
            log.error("addProductPresent 新品上报中台 失败，resp: {}", JSON.toJSONString(resp));
        }
        return resp;
    }

    // endregion


    // region 三方外采上报中台（智鹿）
    @Override
    public String addProductPresentAndErpInfo(@Nonnull ProductInfoDto dto) {
        MidGeneralProductPresentInfoVo vo = MidGeneralProductPresentInfoVo.of(dto);
        log.info("addProductPresentAndErpInfo 调用中台三方外采上报参数：{}", JSONUtil.toJsonStr(vo));

        MidResponse2<Boolean> resp = dubboForwardClient.addProductPresentAndErpInfo(new Param()
            .typeList("com.xyy.me.product.general.api.vo.present.GeneralProductPresentInfoVo")
            .valueList(vo));

        log.info("addProductPresentAndErpInfo 三方外采上报中台（智鹿）req: {}，resp: {}", JSON.toJSONString(vo), JSON.toJSONString(resp));
        if (resp == null || resp.isFailure()) {
            log.error("addProductPresentAndErpInfo 三方外采上报中台（智鹿）失败，resp: {}", JSON.toJSONString(resp));
            String msg = Optional.ofNullable(resp).map(MidResponse2::getRetMsg).orElse(null);
            // 五要素相同不能重复上报, 直接视为成功
            return "五要素相同不能重复上报".equals(msg) ? null : msg;
        }
        return null;
    }



    // endregion

    
    // region 批量查询中台标品
    @Override
    public List<MidGeneralProductDto> getGeneralProductList(List<Long> midStdlibIdList) {
        if (CollectionUtils.isEmpty(midStdlibIdList)) {
            return List.of();
        }

        MidGeneralProductVo vo = new MidGeneralProductVo()
                .setTraceId(MeProductTransformUtil.callTraceId())
                .setProductIdList(midStdlibIdList.stream().map(String::valueOf).toList());
        // log.info("getGeneralProductList 调用中台标品查询参数：{}", JSONUtil.toJsonStr(vo));
        MidResponse2<List<MidGeneralProductDto>> midResponse2 = dubboForwardClient.getGeneralProductList(
                new Param()
                        .typeList("com.xyy.me.product.general.api.vo.product.GeneralProductVo")
                        .valueList(vo));
        if (midResponse2 == null || midResponse2.isFailure()) {
            log.error("getGeneralProductList 调用中台标品查询失败，resp: {}", JSON.toJSONString(midResponse2));
            return List.of();
        }
        return midResponse2.getData();
    }

    // endregion


    // region 批量查询中台标品图片
    @Override
    public List<MidPictureProResult> findProPicture(List<Long> midStdlibIdList) {
        if (CollectionUtils.isEmpty(midStdlibIdList)) {
            return List.of();
        }

        // 商品id或者商品编号个数不能超过100
        return Lists.partition(midStdlibIdList, 100).stream().flatMap(list -> {
            MidPictureCondition midPictureCondition = new MidPictureCondition()
                .setProductIds(list.stream().map(String::valueOf).toList());
            // log.info("findProPicture 调用中台标品图片查询参数：midStdlibIdList: {}", JSONUtil.toJsonStr(midStdlibIdList));
            MidResponse3<List<MidPictureProResult>> responseInfo = dubboForwardClient.findProPicture(new Param()
                .typeList("com.xyy.me.product.general.api.dto.pictrue.PictrueCondition")
                .valueList(midPictureCondition)
            );
            if (responseInfo == null || responseInfo.isFailure() || responseInfo.getData() == null) {
                log.error("findProPicture 调用中台标品图片查询失败，midStdlibIdList:{}, resp: {}", list, responseInfo);
                return Stream.empty();
            }
            return responseInfo.getData().stream();
        }).toList();
    }
    // endregion


    // region 批量查询中台字典数据
    @Override
    public PageResult<MidTotalDictionaryReadDto> pageQueryDictionaryUpgrade(MidDictionaryVo vo) {
        vo.setTraceId(MeProductTransformUtil.callTraceId());
        log.info("pageQueryDictionaryUpgrade 调用中台字典数据查询参数：{}", JSONUtil.toJsonStr(vo));
        MidResponse2<PageResult<MidTotalDictionaryReadDto>> resp = dubboForwardClient.pageQueryDictionaryUpgrade(new Param()
            .typeList("com.xyy.me.product.service.read.vo.DictionaryVo")
            .valueList(vo));
        if (resp == null || resp.isFailure()) {
            log.error("pageQueryDictionaryUpgrade 调用中台字典数据查询失败，vo:{}, resp: {}", vo, resp);
            return PageResult.empty();
        }
        return resp.getData();
    }

    /**
     * 通过上级 id 获取子级分类
     *
     * @param parentCategoryId 传空或者 0 获取第一级分类
     * @return list
     */
    @Override
    public List<SaasCategoryDto> queryCategoryByParentId(Integer parentCategoryId) {
        log.info("queryCategoryByParentId 通过上级 id 获取子级分类参数：{}", parentCategoryId);

        MidResponse2<List<SaasCategoryDto>>  resp = dubboForwardClient.queryCategoryByParentId(new Param()
            .typeList("java.lang.Integer")
            .valueList(parentCategoryId));

        if(resp == null || resp.isFailure()){
            log.info("调用中台分类失败,method:queryCategoryByParentId,id:{},msg:{}", parentCategoryId,resp == null ? "": resp.getRetMsg());
            return new ArrayList<>();
        }

        return JSON.parseObject(JSON.toJSONString(resp.getData()), new TypeReference<List<SaasCategoryDto>>() {});
    }

    /**
     * 通过商品名称获取商品六级分类
     *
     * @param productName 商品名称
     * @return list
     */
    @Override
    public List<List<SaasCategoryDto>> queryProductCategoryByProductName(String productName) {
        log.info("queryProductCategoryByProductName 通过商品名称获取商品六级分类参数：{}", productName);

        MidResponse2<List<List<SaasCategoryDto>>> responseInfo = dubboForwardClient.queryProductCategoryByProductName(new Param()
            .typeList("java.lang.String")
            .valueList(productName));

        if(responseInfo == null || responseInfo.isFailure()){
            log.info("调用中台分类失败,method:queryProductCategoryByProductName,productName:{},msg:{}", productName,responseInfo == null ? "": responseInfo.getRetMsg());
            return new ArrayList<>();
        }

        return JSON.parseObject(JSON.toJSONString(responseInfo.getData()), new TypeReference<List<List<SaasCategoryDto>>>() {});
    }

    /**
     * 通过商品名称获取商品六级分类
     *
     * @param productNames 商品名称列表
     * @return map
     */
    @Override
    public Map<String, List<List<SaasCategoryDto>>> queryProductCategoryByProductNameList(List<String> productNames) {
        if(org.apache.commons.collections.CollectionUtils.isEmpty(productNames)){
            return new HashMap<>();
        }


        MidResponse2<Map<String, List<List<SaasCategoryDto>>>> responseInfo = dubboForwardClient.queryProductCategoryByProductNameList(new Param()
            .typeList("java.util.List")
            .valueList(productNames));

        if(responseInfo == null || responseInfo.isFailure()){
            log.info("调用中台分类失败,method:queryProductCategoryByProductNameList,productNames:{},msg:{}", productNames,responseInfo == null ? "": responseInfo.getRetMsg());
            return new HashMap<>();
        }

        return JSON.parseObject(JSON.toJSONString(responseInfo.getData()), new TypeReference<Map<String, List<List<SaasCategoryDto>>>>() {});
    }

    /**
     * 根据id 查询分类链路
     *
     * @param endIds 末级分类id
     * @return
     */
    @Override
    public Map<Integer, List<List<SaasCategoryDto>>> queryCategoryPathByIds(List<Integer> endIds) {
        if(org.apache.commons.collections.CollectionUtils.isEmpty(endIds)){
            return new HashMap<>();
        }

        MidResponse2<Map<Integer, List<List<SaasCategoryDto>>>> responseInfo = dubboForwardClient.queryCategoryPathByIds(new Param()
            .typeList("java.util.List")
            .valueList(endIds));


        if(responseInfo == null || responseInfo.isFailure()){
            log.info("调用中台分类失败,method:queryCategoryPathByIds,ids:{},msg:{}", JSON.toJSONString(endIds),responseInfo == null ? "": responseInfo.getRetMsg());
            return new HashMap<>();
        }

        return JSON.parseObject(JSON.toJSONString(responseInfo.getData()), new TypeReference<Map<Integer, List<List<SaasCategoryDto>>>>() {});
    }

    /**
     * 分类名称模糊匹配上级链路 - 分页
     *
     * @param name
     * @param pageNum  从0开始
     * @param pageSize
     * @return
     */
    @Override
    public PageResult<List<SaasCategoryDto>> queryCategoryPathByNameFuzzy(String name, Integer pageNum, Integer pageSize) {

        MidResponse2<PageResult<List<SaasCategoryDto>>> responseInfo = dubboForwardClient.queryCategoryPathByNameFuzzy(new Param()
            .typeList("java.lang.String","java.lang.Integer","java.lang.Integer")
            .valueList(name, pageNum-1, pageSize));

        if(responseInfo == null || responseInfo.isFailure()){
            log.info("调用中台分类失败,method:queryCategoryPathByNameFuzzy,name:{},msg:{}", name,responseInfo == null ? "": responseInfo.getRetMsg());
            return new PageResult<>();
        }

        return JSON.parseObject(JSON.toJSONString(responseInfo.getData()), new TypeReference<PageResult<List<SaasCategoryDto>>>() {});
    }

    /**
     * 根据六级分类名称查看分类链路
     *
     * @param names
     * @return
     */
    @Override
    public Map<String, List<List<SaasCategoryDto>>> queryCategoryPathByNames(List<String> names) {

        MidResponse2<Map<String, List<List<SaasCategoryDto>>>> responseInfo = dubboForwardClient.queryCategoryPathByNames(new Param()
            .typeList("java.util.List")
            .valueList(names));

        if(responseInfo == null || responseInfo.isFailure() || responseInfo.getData() == null){
            log.info("调用中台分类失败,method:queryCategoryPathByNames,names:{},msg:{}", JSON.toJSONString(names),responseInfo == null ? "": responseInfo.getRetMsg());
            return new HashMap<>();
        }
        return JSON.parseObject(JSON.toJSONString(responseInfo.getData()), new TypeReference<Map<String, List<List<SaasCategoryDto>>>>() {});
    }
    // endregion
    
}
