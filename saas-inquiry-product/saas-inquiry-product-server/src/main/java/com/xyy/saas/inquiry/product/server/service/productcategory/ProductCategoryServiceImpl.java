package com.xyy.saas.inquiry.product.server.service.productcategory;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_CATEGORY_EXITS_CHILDREN;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_CATEGORY_NAME_DUPLICATE;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_CATEGORY_NOT_EXISTS;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_CATEGORY_PARENT_ERROR;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_CATEGORY_PARENT_IS_CHILD;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_CATEGORY_PARENT_NOT_EXITS;

import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo.ProductCategoryListReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo.ProductCategoryRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo.ProductCategorySaveReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.category.ProductCategoryDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.productcategory.ProductCategoryMapper;
import com.xyy.saas.inquiry.product.server.dal.redis.RedisKeyConstants;
import com.xyy.saas.inquiry.product.server.dal.redis.ProductCategoryCacheConstants;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

/**
 * 商品六级分类 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductCategoryServiceImpl implements ProductCategoryService {

    @Resource
    private ProductCategoryMapper productCategoryMapper;

    @Resource
    @Lazy
    private ProductCategoryService selfProxy;

    /**
     * 本地缓存：缓存热点分类数据
     * 通过配置类注入，避免硬编码配置参数
     */
    @Resource
    @Qualifier("productCategoryLocalCache")
    private Cache<String, List<ProductCategoryRespVO>> localCache;

    @Resource
    private CacheManager cacheManager;

    @Override
    public void batchSaveOrUpdate(List<ProductCategoryDO> productCategories) {
        if (productCategories == null || productCategories.isEmpty()) {
            return;
        }
        // 获取所有dictId列表（去重）
        Map<Long, ProductCategoryDO> dictIdMap = productCategories.stream()
                .collect(Collectors.toMap(ProductCategoryDO::getDictId, Function.identity(), (a, b) -> b));

        // 查询所有已存在的分类
        List<ProductCategoryDO> existingCategories = productCategoryMapper.selectByDictIdList(new ArrayList<>(dictIdMap.keySet()));
        Map<Long, ProductCategoryDO> existingCategoryMap = existingCategories.stream()
                .collect(Collectors.toMap(ProductCategoryDO::getDictId, Function.identity(), (o1, o2) -> o1));

        // 分别收集需要新增和更新的记录
        List<ProductCategoryDO> toInsert = new ArrayList<>();
        List<ProductCategoryDO> toUpdate = new ArrayList<>();

        for (ProductCategoryDO category : dictIdMap.values()) {
            ProductCategoryDO existingCategory = existingCategoryMap.get(category.getDictId());
            if (existingCategory == null) {
                // 不存在则新增
                toInsert.add(category);
            } else if (category.checkIsChanged(existingCategory)) {
                // 存在则更新（判断是否有改动）
                category.setId(existingCategory.getId());
                toUpdate.add(category);
            }
        }

        // 批量新增和更新
        boolean success = false;
        if (!toInsert.isEmpty()) {
            success |= productCategoryMapper.insertBatch(toInsert) == Boolean.TRUE;
        }
        if (!toUpdate.isEmpty()) {
            success |= productCategoryMapper.updateBatch(toUpdate);
        }
        if (success) {
            // 批量操作，清除所有缓存
            clearAllCategoryCache();
        }
    }

    @Override
    public Long createProductCategory(ProductCategorySaveReqVO createReqVO) {
        // 校验字典父id（中台）的有效性
        validateParentProductCategory(null, createReqVO.getParentDictId());
        // 校验分类名称的唯一性
        validateProductCategoryNameUnique(null, createReqVO.getParentDictId(), createReqVO.getName());

        // 插入
        ProductCategoryDO productCategory = BeanUtils.toBean(createReqVO, ProductCategoryDO.class);
        int cnt = productCategoryMapper.insert(productCategory);
        if (cnt > 0) {
            // 精确清理相关缓存
            clearCategoryCache(productCategory.getDictId(), createReqVO.getParentDictId());
        }
        // 返回
        return productCategory.getId();
    }

    @Override
    public int updateProductCategory(ProductCategorySaveReqVO updateReqVO) {
        // 校验存在
        ProductCategoryDO categoryDO = validateProductCategoryExists(updateReqVO.getId());
        // 校验字典父id（中台）的有效性
        validateParentProductCategory(categoryDO.getDictId(), updateReqVO.getParentDictId());
        // 校验分类名称的唯一性
        validateProductCategoryNameUnique(categoryDO.getDictId(), updateReqVO.getParentDictId(), updateReqVO.getName());

        // 更新
        ProductCategoryDO updateObj = BeanUtils.toBean(updateReqVO, ProductCategoryDO.class);
        int cnt = productCategoryMapper.updateById(updateObj);
        if (cnt > 0) {
            // 精确清理相关缓存
            clearCategoryCache(categoryDO.getDictId(), updateReqVO.getParentDictId());
        }
        return cnt;
    }

    @Override
    public void deleteProductCategory(Long id) {
        // 校验存在
        ProductCategoryDO categoryDO = validateProductCategoryExists(id);
        // 校验是否有子商品六级分类
        if (productCategoryMapper.selectCountByParentDictId(categoryDO.getDictId()) > 0) {
            throw exception(PRODUCT_CATEGORY_EXITS_CHILDREN);
        }
        // 删除
        int cnt = productCategoryMapper.deleteById(id);
        if (cnt > 0) {
            // 精确清理相关缓存
            clearCategoryCache(categoryDO.getDictId(), categoryDO.getParentDictId());
        }
    }

    private ProductCategoryDO validateProductCategoryExists(Long id) {
        return Optional.ofNullable(productCategoryMapper.selectById(id)).orElseThrow(() -> exception(PRODUCT_CATEGORY_NOT_EXISTS));
    }

    private void validateParentProductCategory(Long dictId, Long parentDictId) {
        if (parentDictId == null || ProductCategoryDO.PARENT_DICT_ID_ROOT.equals(parentDictId)) {
            return;
        }
        // 1. 不能设置自己为父商品六级分类
        if (Objects.equals(dictId, parentDictId)) {
            throw exception(PRODUCT_CATEGORY_PARENT_ERROR);
        }
        // 2. 父商品六级分类不存在
        ProductCategoryDO parentProductCategory = productCategoryMapper.selectByDictId(parentDictId);
        if (parentProductCategory == null) {
            throw exception(PRODUCT_CATEGORY_PARENT_NOT_EXITS);
        }
        // 3. 递归校验父商品六级分类，如果父商品六级分类是自己的子商品六级分类，则报错，避免形成环路
        if (dictId == null) { // id 为空，说明新增，不需要考虑环路
            return;
        }
        for (int i = 0; i < Short.MAX_VALUE; i++) {
            // 3.1 校验环路
            parentDictId = parentProductCategory.getParentDictId();
            if (Objects.equals(dictId, parentDictId)) {
                throw exception(PRODUCT_CATEGORY_PARENT_IS_CHILD);
            }
            // 3.2 继续递归下一级父商品六级分类
            if (parentDictId == null || ProductCategoryDO.PARENT_DICT_ID_ROOT.equals(parentDictId)) {
                break;
            }
            parentProductCategory = productCategoryMapper.selectByDictId(parentDictId);
            if (parentProductCategory == null) {
                break;
            }
        }
    }

    private void validateProductCategoryNameUnique(Long dictId, Long parentDictId, String name) {
        ProductCategoryDO productCategory = productCategoryMapper.selectByParentDictIdAndName(parentDictId, name);
        if (productCategory == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的商品六级分类
        if (dictId == null) {
            throw exception(PRODUCT_CATEGORY_NAME_DUPLICATE);
        }
        if (!Objects.equals(productCategory.getDictId(), dictId)) {
            throw exception(PRODUCT_CATEGORY_NAME_DUPLICATE);
        }
    }

    @Override
    public ProductCategoryDO getProductCategory(Long id) {
        return productCategoryMapper.selectById(id);
    }

    @Override
    public List<ProductCategoryDO> getProductCategoryList(ProductCategoryListReqVO listReqVO) {
        return productCategoryMapper.selectList(listReqVO);
    }


    @Override
    public void clearProductCategoryTreeCache() {
        // 清除所有相关缓存
        clearAllCategoryCache();
    }

    /**
     * 清除所有分类缓存
     */
    @CacheEvict(value = {
        RedisKeyConstants.PRODUCT_CATEGORY_CHILDREN,
        RedisKeyConstants.PRODUCT_CATEGORY_DETAIL,
        RedisKeyConstants.PRODUCT_CATEGORY_TREE_HOT
    }, allEntries = true)
    private void clearAllCategoryCache() {
        // 清除本地缓存
        localCache.invalidateAll();
    }

    /**
     * 精确清除指定分类的相关缓存
     */
    private void clearCategoryCache(Long dictId, Long parentDictId) {
        // 清除当前分类详情缓存
        if (cacheManager.getCache(RedisKeyConstants.PRODUCT_CATEGORY_DETAIL) != null) {
            cacheManager.getCache(RedisKeyConstants.PRODUCT_CATEGORY_DETAIL).evict(dictId);
        }

        // 清除父分类的子分类列表缓存
        if (parentDictId != null) {
            if (cacheManager.getCache(RedisKeyConstants.PRODUCT_CATEGORY_CHILDREN) != null) {
                cacheManager.getCache(RedisKeyConstants.PRODUCT_CATEGORY_CHILDREN).evict(parentDictId);
            }
        } else {
            // 如果是根分类，清除第1级缓存
            if (cacheManager.getCache(RedisKeyConstants.PRODUCT_CATEGORY_LEVEL_PREFIX + "1") != null) {
                cacheManager.getCache(RedisKeyConstants.PRODUCT_CATEGORY_LEVEL_PREFIX + "1").clear();
            }
        }

        // 清除本地缓存中的相关数据
        localCache.invalidate(ProductCategoryCacheConstants.LOCAL_CACHE_KEY_ROOT_CATEGORIES);
        localCache.invalidate(ProductCategoryCacheConstants.LOCAL_CACHE_KEY_CHILDREN_PREFIX + parentDictId);
    }

    /**
     * 获得商品六级分类树（只返回根分类，支持按需加载）
     *
     * @return 根分类列表
     */
    @Override
    public List<ProductCategoryRespVO> getProductCategoryTree() {
        return getRootCategories();
    }

    /**
     * 获取根分类（第1级分类）
     */
    @Cacheable(value = RedisKeyConstants.PRODUCT_CATEGORY_LEVEL_PREFIX + "1", unless = "#result == null")
    public List<ProductCategoryRespVO> getRootCategories() {
        // 先尝试从本地缓存获取
        String localCacheKey = ProductCategoryCacheConstants.LOCAL_CACHE_KEY_ROOT_CATEGORIES;
        List<ProductCategoryRespVO> rootCategories = localCache.getIfPresent(localCacheKey);

        if (rootCategories != null) {
            return rootCategories;
        }

        // 查询第1级分类（parentDictId = 0）
        List<ProductCategoryDO> rootList = productCategoryMapper.selectList(
            new LambdaQueryWrapper<ProductCategoryDO>()
                .eq(ProductCategoryDO::getParentDictId, ProductCategoryDO.PARENT_DICT_ID_ROOT)
                .orderByDesc(ProductCategoryDO::getUpdateTime)
        );

        rootCategories = BeanUtils.toBean(rootList, ProductCategoryRespVO.class);

        // 缓存到本地
        localCache.put(localCacheKey, rootCategories);

        return rootCategories;
    }

    /**
     * 获取指定父分类的子分类
     */
    @Cacheable(value = RedisKeyConstants.PRODUCT_CATEGORY_CHILDREN, key = "#parentDictId", unless = "#result == null")
    public List<ProductCategoryRespVO> getChildCategories(Long parentDictId) {
        if (parentDictId == null) {
            return getRootCategories();
        }

        // 先尝试从本地缓存获取
        String localCacheKey = ProductCategoryCacheConstants.LOCAL_CACHE_KEY_CHILDREN_PREFIX + parentDictId;
        List<ProductCategoryRespVO> childCategories = localCache.getIfPresent(localCacheKey);

        if (childCategories != null) {
            return childCategories;
        }

        List<ProductCategoryDO> childList = productCategoryMapper.selectList(
            new LambdaQueryWrapper<ProductCategoryDO>()
                .eq(ProductCategoryDO::getParentDictId, parentDictId)
                .orderByDesc(ProductCategoryDO::getUpdateTime)
        );

        childCategories = BeanUtils.toBean(childList, ProductCategoryRespVO.class);

        // 缓存到本地（只缓存热点数据）
        if (childCategories.size() <= 50) { // 限制缓存大小
            localCache.put(localCacheKey, childCategories);
        }

        return childCategories;
    }

    /**
     * 获取分类详情
     */
    @Cacheable(value = RedisKeyConstants.PRODUCT_CATEGORY_DETAIL, key = "#dictId", unless = "#result == null")
    public ProductCategoryRespVO getCategoryDetail(Long dictId) {
        ProductCategoryDO category = productCategoryMapper.selectOne(
            new LambdaQueryWrapper<ProductCategoryDO>()
                .eq(ProductCategoryDO::getDictId, dictId)
        );
        return BeanUtils.toBean(category, ProductCategoryRespVO.class);
    }

    /**
     * 构建分类树（按需加载，支持指定深度）
     *
     * @param parentDictId 父分类ID，null表示从根开始
     * @param maxDepth 最大深度
     * @return 分类树
     */
    public List<ProductCategoryRespVO> getProductCategoryTreeWithChildren(Long parentDictId, int maxDepth) {
        if (maxDepth <= 0) {
            return new ArrayList<>();
        }

        List<ProductCategoryRespVO> categories = getChildCategories(parentDictId);

        // 递归加载子分类（限制深度）
        if (maxDepth > 1) {
            for (ProductCategoryRespVO category : categories) {
                List<ProductCategoryRespVO> children = getProductCategoryTreeWithChildren(
                    category.getDictId(), maxDepth - 1);
                category.setChildren(children);
            }
        }

        return categories;
    }
}