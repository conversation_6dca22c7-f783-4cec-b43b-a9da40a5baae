package com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

@Schema(description = "管理后台 - 售价调整单明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProductPriceAdjustmentDetailRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13165")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "单据编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "16932")
    @ExcelProperty("单据编号")
    private String recordPref;

    @Schema(description = "适用门店", requiredMode = Schema.RequiredMode.REQUIRED, example = "2585")
    private Long applicableTenantId;

    @Schema(description = "适用门店", requiredMode = Schema.RequiredMode.REQUIRED, example = "2585")
    @ExcelProperty("适用门店")
    private String applicableTenantName;

    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "22067")
    @ExcelProperty("商品编码")
    private String productPref;

    @Schema(description = "原零售价", requiredMode = Schema.RequiredMode.REQUIRED, example = "14358")
    @ExcelProperty("原零售价")
    private BigDecimal oldRetailPrice;

    @Schema(description = "原会员价", requiredMode = Schema.RequiredMode.REQUIRED, example = "27197")
    @ExcelProperty("原会员价")
    private BigDecimal oldMemberPrice;

    @Schema(description = "新零售价", requiredMode = Schema.RequiredMode.REQUIRED, example = "3727")
    @ExcelProperty("新零售价")
    private BigDecimal newRetailPrice;

    @Schema(description = "新会员价", requiredMode = Schema.RequiredMode.REQUIRED, example = "31530")
    @ExcelProperty("新会员价")
    private BigDecimal newMemberPrice;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;
    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2025-01-01 00:00:00")
    private LocalDateTime updateTime;
    @Schema(description = "创建人ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String creator;
    @Schema(description = "创建人名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String creatorName;
    @Schema(description = "更新人ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String updater;
    @Schema(description = "更新人名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String updaterName;



    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品编码")
    private String pref;

    @Schema(description = "商品外码（自动生成或填写，机构唯一）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品外码（自动生成或填写，机构唯一）")
    private String showPref;

    @Schema(description = "标准库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21163")
    @ExcelProperty("标准库ID")
    private Long stdlibId;

    @Schema(description = "助记码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("助记码")
    private String mnemonicCode;

    @Schema(description = "通用名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("通用名")
    private String commonName;

    @Schema(description = "品牌名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("品牌名")
    private String brandName;

    @Schema(description = "规格/型号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("规格/型号")
    private String spec;

    @Schema(description = "条形码（基本单位条形码）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("条形码")
    private String barcode;

    @Schema(description = "生产厂家", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("生产厂家")
    private String manufacturer;

    @Schema(description = "批准文号（商品分类为医疗器械时，变为备案/注册证）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("批准文号（商品分类为医疗器械时，变为备案/注册证）")
    private String approvalNumber;

}