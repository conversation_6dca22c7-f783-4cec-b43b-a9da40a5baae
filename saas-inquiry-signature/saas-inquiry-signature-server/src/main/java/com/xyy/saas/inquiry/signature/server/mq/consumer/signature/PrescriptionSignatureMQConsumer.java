package com.xyy.saas.inquiry.signature.server.mq.consumer.signature;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.signature.server.mq.message.PrescriptionSignatureEvent;
import com.xyy.saas.inquiry.signature.server.service.prescription.InquirySignaturePrescriptionService;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Desc 处方签章回调MQ
 * <AUTHOR>
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_signature_server_mq_consumer_PrescriptionSignatureMQConsumer",
    topic = PrescriptionSignatureEvent.TOPIC, consumerThreadNums = "8")
public class PrescriptionSignatureMQConsumer {

    @Resource
    private InquirySignaturePrescriptionService inquirySignaturePrescriptionService;

    @EventBusListener
    public void prescriptionSignatureMQConsumer(PrescriptionSignatureEvent prescriptionSignatureEvent) {
        inquirySignaturePrescriptionService.signaturePrescriptionCallback(prescriptionSignatureEvent.getMsg().setCallBackTime(LocalDateTime.now()));
    }

}
