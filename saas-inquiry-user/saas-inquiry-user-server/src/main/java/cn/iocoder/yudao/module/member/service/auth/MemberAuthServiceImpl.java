package cn.iocoder.yudao.module.member.service.auth;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.enums.TerminalEnum;
import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.framework.common.util.monitor.TracerUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.member.controller.admin.auth.vo.ZhlDeleteAccountReqVO;
import cn.iocoder.yudao.module.member.controller.admin.user.vo.MemberUserUpdateReqVO;
import cn.iocoder.yudao.module.member.controller.app.auth.vo.AppAuthLoginRespVO;
import cn.iocoder.yudao.module.member.controller.app.auth.vo.AppAuthWeixinMiniAppLoginReqVO;
import cn.iocoder.yudao.module.member.controller.app.auth.vo.AppAuthWeixinMiniAppUserMobileReqVO;
import cn.iocoder.yudao.module.member.controller.app.auth.vo.MobileLoginReqVO;
import cn.iocoder.yudao.module.member.controller.admin.auth.vo.ZhlBindAccountReqVO;
import cn.iocoder.yudao.module.member.controller.admin.auth.vo.ZhlBindAccountRespVO;
import cn.iocoder.yudao.module.member.controller.admin.auth.vo.ZhlBindTenantReqVO;
import cn.iocoder.yudao.module.member.controller.admin.auth.vo.ZhlBindTenantRespVO;
import cn.iocoder.yudao.module.member.controller.admin.auth.vo.ZhlTicketLoginReqVO;
import cn.iocoder.yudao.module.member.controller.app.auth.vo.WeixinMiniAppAuthUserMobileSwitchReqVO;
import cn.iocoder.yudao.module.member.controller.app.user.vo.AppMemberUserUpdateMobileReqVO;
import cn.iocoder.yudao.module.member.convert.auth.AuthConvert;
import cn.iocoder.yudao.module.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.member.dal.mysql.user.MemberUserMapper;
import cn.iocoder.yudao.module.member.service.user.MemberUserService;
import cn.iocoder.yudao.module.system.api.logger.LoginLogApi;
import cn.iocoder.yudao.module.system.api.logger.dto.LoginLogCreateReqDTO;
import cn.iocoder.yudao.module.system.api.oauth2.OAuth2TokenApi;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenCreateReqDTO;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenRespDTO;
import cn.iocoder.yudao.module.system.api.sms.SmsCodeApi;
import cn.iocoder.yudao.module.system.api.social.SocialClientApi;
import cn.iocoder.yudao.module.system.api.social.SocialUserApi;
import cn.iocoder.yudao.module.system.api.social.dto.SocialUserBindReqDTO;
import cn.iocoder.yudao.module.system.api.social.dto.SocialUserReqDTO;
import cn.iocoder.yudao.module.system.api.social.dto.SocialUserRespDTO;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.BindMobileReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.biz.BusinessTypeDO;
import cn.iocoder.yudao.module.system.dal.redis.common.UserLockRedisDAO;
import cn.iocoder.yudao.module.system.controller.admin.socail.vo.user.SocialUserPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantSimpleRespVO;
import cn.iocoder.yudao.module.system.dal.dataobject.social.SocialUserBindDO;
import cn.iocoder.yudao.module.system.dal.dataobject.social.SocialUserDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.yudao.module.system.enums.logger.LoginLogTypeEnum;
import cn.iocoder.yudao.module.system.enums.logger.LoginResultEnum;
import cn.iocoder.yudao.module.system.enums.oauth2.OAuth2ClientConstants;
import cn.iocoder.yudao.module.system.enums.social.SocialTypeEnum;
import cn.iocoder.yudao.module.system.service.biz.BusinessTypeService;
import cn.iocoder.yudao.module.system.service.tenant.TenantService;
import cn.iocoder.yudao.module.system.service.auth.AdminAuthService;
import cn.iocoder.yudao.module.system.service.permission.PermissionService;
import cn.iocoder.yudao.module.system.service.social.SocialUserService;
import cn.iocoder.yudao.module.system.service.user.AdminUserService;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.servlet.ServletUtils.getClientIP;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.AUTH_LOGIN_USER_DISABLED;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_NOT_EXISTS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.WX_MINI_APP_AUTH_USER_PHONE_FAILD;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.ZHL_TICKET_LOGIN_ERROR;

/**
 * 会员的认证 Service 接口
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MemberAuthServiceImpl implements MemberAuthService{

    @Resource
    private SocialClientApi socialClientApi;

    @Resource
    private BusinessTypeService businessTypeService;

    @Resource
    private MemberUserService userService;

    @Resource
    private SocialUserApi socialUserApi;

    @Resource
    private LoginLogApi loginLogApi;

    @Resource
    private OAuth2TokenApi oauth2TokenApi;

    @Resource
    private AdminAuthService adminAuthService;

    @Resource
    private AdminUserService adminUserService;

    @Resource
    private SocialUserService socialUserService;

    @Resource
    private PermissionService permissionService;

    @Resource
    private UserLockRedisDAO userLockRedisDAO;

    @Resource
    private SmsCodeApi smsCodeApi;

    @Resource
    private TenantService tenantService;

    @Resource
    private MemberUserMapper userMapper;

    /**
     * 微信小程序的一键登录
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    @Override
    public AppAuthLoginRespVO weixinMiniAppLogin(AppAuthWeixinMiniAppLoginReqVO reqVO) {
        // 获取社交用户信息
        SocialUserRespDTO socialUserRespDTO = socialUserApi.getSocialUserByCode(getUserType().getValue(),reqVO.getSocialType(),reqVO.getClientType(),reqVO.getBusinessType(), reqVO.getLoginCode(), reqVO.getState());
        MemberUserDO user = null;
        // 是否绑定memberUser
        if(ObjectUtil.isEmpty(socialUserRespDTO.getUserId())){
            // 新增用户
            user = userService.createUser(socialUserRespDTO.getNickname(),socialUserRespDTO.getAvatar(),getClientIP(),TerminalEnum.WECHAT_MINI_PROGRAM.getTerminal());
        }else{
            // 获取用户
            user = userService.getUser(socialUserRespDTO.getUserId());
        }
        // 绑定社交用户
        String openid = socialUserApi.bindSocialUser(new SocialUserBindReqDTO(user.getId(), getUserType().getValue(), reqVO.getSocialType(),
            reqVO.getBusinessType(),reqVO.getClientType(), reqVO.getLoginCode(), reqVO.getState()));

        // 创建 Token 令牌，记录登录日志
        String clientId = getClientId(reqVO.getBusinessType(), reqVO.getClientType(), getUserType().getValue());
        AppAuthLoginRespVO loginRespVO = createTokenAfterLoginSuccess(user, user.getMobile(), LoginLogTypeEnum.LOGIN_SOCIAL, openid, clientId);
        loginRespVO.setMobile(user.getMobile());
        return loginRespVO;
    }

    /**
     * 统一处理手机号更新逻辑
     *
     * @param targetUserId 目标用户ID
     * @param useVerificationCode 是否使用验证码验证
     * @param updateMobileReq 更新手机号请求（验证码场景使用）
     */
    private void updateUserMobileInternal(Long targetUserId, boolean useVerificationCode,
                                        AppMemberUserUpdateMobileReqVO updateMobileReq) {
        if (useVerificationCode) {
            userService.updateUserMobile(targetUserId, updateMobileReq);
        } else {
            // 微信绑定场景，创建微信更新请求
            userMapper.updateById(MemberUserDO.builder().id(targetUserId).mobile(updateMobileReq.getMobile()).build());
        }
    }

    /**
     * 绑定手机号核心逻辑
     *
     * @param reqVO 更新手机号请求
     * @param useVerificationCode 是否使用验证码验证
     * @return 已绑定的手机号
     */
    private String bindingMobileCore(BindMobileReqVO reqVO, boolean useVerificationCode) {

        // 验证锁定
        userLockRedisDAO.isLocked(reqVO.getBindMobileReq().getMobile());

        // 查询手机号是否已绑定
        MemberUserDO orgUser = userService.getUserByMobile(reqVO.getBindMobileReq().getMobile());


        if (orgUser != null) {
            // 手机号已存在，需要合并用户信息
            MemberUserDO currentUser = userService.getUser(reqVO.getUserId());
            Assert.notNull(currentUser, "用户不能为空");

            // 将当前用户的信息合并到已存在的用户
            orgUser.setAvatar(currentUser.getAvatar());
            orgUser.setNickname(currentUser.getNickname());
            orgUser.setSex(currentUser.getSex());

            // 标记当前用户为删除状态
            currentUser.setDeleted(true);
            MemberUserUpdateReqVO deleteReqVo = BeanUtils.toBean(currentUser, MemberUserUpdateReqVO.class);
            MemberUserUpdateReqVO updateReqVo = BeanUtils.toBean(orgUser, MemberUserUpdateReqVO.class);

            // 执行用户更新和删除操作
            userService.updateUser(updateReqVo);
            userService.updateUser(deleteReqVo);

            // 更新手机号信息
            updateUserMobileInternal(orgUser.getId(), useVerificationCode, reqVO.getBindMobileReq());

            // 更新社交用户绑定关系
            socialUserService.updateBoundSocialUser(getUserType().getValue(), reqVO.getState(), currentUser.getId(),
                orgUser.getId(), reqVO.getSocialType());
        } else {
            // 手机号不存在，直接更新当前用户的手机号
            updateUserMobileInternal(reqVO.getUserId(), useVerificationCode, reqVO.getBindMobileReq());
        }

        return reqVO.getBindMobileReq().getMobile();
    }

    /**
     * 绑定手机号
     *
     * @param reqVO 绑定手机号请求入参
     * @return 已绑定的手机号
     */
    @Override
     @Transactional(rollbackFor = Exception.class)
     public String bindingMobile(BindMobileReqVO reqVO) {
         // 调用核心绑定逻辑
         return bindingMobileCore(reqVO,true);
     }

    /**
     * 获取微信用户手机号并绑定
     *
     * @param reqVO 绑定手机号请求入参
     * @return 已绑定的手机号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String bindingWXUserMobile(AppAuthWeixinMiniAppUserMobileReqVO reqVO) {

        String wxSocialUserMobile = socialUserService.getWXSocialUserMobile(reqVO.getUserType(), reqVO.getBusinessType(), reqVO.getPhoneCode());

        if(wxSocialUserMobile == null){
            throw exception(WX_MINI_APP_AUTH_USER_PHONE_FAILD);
        }

        // 绑定手机号 - 复用bindingMobileCore方法，不使用验证码
        AppMemberUserUpdateMobileReqVO updateMobileReq = new AppMemberUserUpdateMobileReqVO();
        updateMobileReq.setMobile(wxSocialUserMobile);
        updateMobileReq.setCode("-1");
        BindMobileReqVO bindMobileReq = BindMobileReqVO.builder()
            .bindMobileReq(updateMobileReq)
            .userId(reqVO.getUserId())
            .socialType(reqVO.getSocialType())
            .clientType(reqVO.getClientType())
            .businessType(reqVO.getBusinessType())
            .state(reqVO.getState())
            .build();
        return bindingMobileCore(bindMobileReq, false);
    }

    @Override
    public AppAuthLoginRespVO mobileLogin(MobileLoginReqVO reqVO){
        //查询用户是否存在
        MemberUserDO user = userService.getUserByMobile(reqVO.getMobile());
        Long userId = Optional.ofNullable(user).map(MemberUserDO::getId).orElse(null);
        SocialUserRespDTO socialUser = socialUserApi.getSocialUserByUserId(getUserType().getValue(), userId, reqVO.getMobile(), reqVO.getSocialType() ,reqVO.getState());

        //新增memberUser
        if(user == null){
            // 新增用户
            user = userService.createUser(socialUser.getNickname(),socialUser.getAvatar(),getClientIP(),TerminalEnum.WECHAT_MINI_PROGRAM.getTerminal());
            AppMemberUserUpdateMobileReqVO updateMobileReq = new AppMemberUserUpdateMobileReqVO();
            updateMobileReq.setMobile(reqVO.getMobile());
            updateMobileReq.setCode(reqVO.getCode());
            userService.updateUserMobile(user.getId(), updateMobileReq);
        }

        // 绑定社交用户
        String openid = socialUserApi.bindSocialUser(new SocialUserBindReqDTO(user.getId(), getUserType().getValue(), reqVO.getSocialType(),
            reqVO.getBusinessType(), reqVO.getClientType(), socialUser.getCode(), reqVO.getState()));

        // 创建 Token 令牌，记录登录日志
        String clientId = getClientId(reqVO.getBusinessType(), reqVO.getClientType(), getUserType().getValue());
        AppAuthLoginRespVO loginRespVO = createTokenAfterLoginSuccess(user, user.getMobile(), LoginLogTypeEnum.LOGIN_SOCIAL, openid, clientId);
        loginRespVO.setMobile(user.getMobile());
        return loginRespVO;
    }

    @Override
    public boolean getWeixinAuthUserMobileSwitch(WeixinMiniAppAuthUserMobileSwitchReqVO reqVO){
        BusinessTypeDO businessType = businessTypeService.getBusinessType(reqVO.getBusinessType(), reqVO.getClientType(), getUserType().getValue());
        if(businessType != null){
            return businessType.getWxMobileAuth();
        }
        return false;
    }

    private String getClientId(Integer businessType,Integer clientType,Integer userType) {
        // 获取客户端id
        BusinessTypeDO businessTypeDO = businessTypeService.getBusinessType(businessType, clientType, userType);
        // 返回结果
        return Optional.ofNullable(businessTypeDO)
            .map(BusinessTypeDO::getClientId)
            .filter(StringUtils::isNotBlank) // 过滤null和空字符串
            .orElse(OAuth2ClientConstants.CLIENT_ID_DEFAULT);
    }

    private AppAuthLoginRespVO createTokenAfterLoginSuccess(MemberUserDO user, String mobile,
        LoginLogTypeEnum logType, String openid, String clientId) {
        // 插入登陆日志
        createLoginLog(user.getId(), mobile, logType, LoginResultEnum.SUCCESS);
        // 创建 Token 令牌
        OAuth2AccessTokenRespDTO accessTokenRespDTO = oauth2TokenApi.createAccessToken(new OAuth2AccessTokenCreateReqDTO()
            .setUserId(user.getId()).setUserType(getUserType().getValue())
            .setClientId(clientId));
        // 构建返回结果
        return AuthConvert.INSTANCE.convert(accessTokenRespDTO, openid);
    }

    private void createLoginLog(Long userId, String mobile, LoginLogTypeEnum logType, LoginResultEnum loginResult) {
        // 插入登录日志
        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        reqDTO.setLogType(logType.getType());
        reqDTO.setTraceId(TracerUtils.getTraceId());
        reqDTO.setUserId(userId);
        reqDTO.setUserType(getUserType().getValue());
        reqDTO.setUsername(mobile);
        reqDTO.setUserAgent(ServletUtils.getUserAgent());
        reqDTO.setUserIp(getClientIP());
        reqDTO.setResult(loginResult.getResult());
        loginLogApi.createLoginLog(reqDTO);
        // 更新最后登录时间
        if (userId != null && Objects.equals(LoginResultEnum.SUCCESS.getResult(), loginResult.getResult())) {
            userService.updateUserLogin(userId, getClientIP());
        }
    }

    private UserTypeEnum getUserType() {
        return UserTypeEnum.MEMBER;
    }

    @Override
    public ZhlBindTenantRespVO zhlBindTenant(ZhlBindTenantReqVO reqVO) {

        AdminUserDO adminUserDO = null;
        if (StringUtils.isNotBlank(reqVO.getPassword())) {
            // 账号密码登录
            adminUserDO = adminAuthService.authenticate(reqVO.getUsername(), reqVO.getPassword());
        } else {
            // 根据手机号获得用户信息
            adminUserDO = adminUserService.getUserByMobileSystem(reqVO.getUsername());
            if (adminUserDO == null) {
                throw exception(USER_NOT_EXISTS);
            }
            // 校验是否禁用
            if (CommonStatusEnum.isDisable(adminUserDO.getStatus())) {
                throw exception(AUTH_LOGIN_USER_DISABLED);
            }
        }

        List<TenantDO> tenantDOList = tenantService.getTenantByAdminUserId(adminUserDO.getId());

        if (CollectionUtils.isEmpty(tenantDOList)) {
            return null;
        }

        List<TenantSimpleRespVO> tenantSimpleRespVOList = tenantDOList.stream()
            .filter(item -> CommonStatusEnum.ENABLE.getStatus().equals(item.getStatus())
                && (CommonStatusEnum.ENABLE.getStatus().equals(item.getWzBizTypeStatus()) || CommonStatusEnum.ENABLE.getStatus().equals(item.getZhlBizTypeStatus())))
            .map(item -> {
                TenantSimpleRespVO tenantSimpleRespVO = new TenantSimpleRespVO();
                tenantSimpleRespVO.setId(item.getId());
                tenantSimpleRespVO.setType(item.getType());
                tenantSimpleRespVO.setPref(item.getPref());
                tenantSimpleRespVO.setName(item.getName());
                tenantSimpleRespVO.setAddress(item.getAddress());
                return tenantSimpleRespVO;
            }).toList();

        ZhlBindTenantRespVO zHlBindTenantRespVO = new ZhlBindTenantRespVO();
        if (tenantSimpleRespVOList.size() == 1) {
            zHlBindTenantRespVO.setTenantId(tenantSimpleRespVOList.getFirst().getId());
        } else {
            zHlBindTenantRespVO.setTenantList(tenantSimpleRespVOList);
        }

        return zHlBindTenantRespVO;
    }

    @Override
    public ZhlBindAccountRespVO zhlBindAccount(ZhlBindAccountReqVO reqVO) {

        String guid = UUID.randomUUID().toString().replace("-", "");
        SocialUserReqDTO socialUserReqDTO = new SocialUserReqDTO();
        socialUserReqDTO.setType(SocialTypeEnum.ZHL.getType());
        socialUserReqDTO.setOpenid(guid);
        socialUserReqDTO.setToken("");
        socialUserReqDTO.setRawTokenInfo("");
        socialUserReqDTO.setNickname("");
        socialUserReqDTO.setAvatar("");
        socialUserReqDTO.setRawUserInfo("");
        socialUserReqDTO.setCode(guid);
        socialUserReqDTO.setState("0");
        // 创建社交用户信息
        SocialUserDO socialUserDO = socialUserService.createSocialUser(socialUserReqDTO);

        // 新增用户
        MemberUserDO user = userService.createUser(socialUserDO.getNickname(), socialUserDO.getAvatar(), getClientIP(), TerminalEnum.H5.getTerminal());
        // 用户绑定智慧脸员工角色
        TenantUtils.execute(reqVO.getTenantId() , () -> permissionService.assignUserRoleWithSystemRoleCodes(user.getId(), Lists.newArrayList(RoleCodeEnum.ZHL_EMPLOYEE.getCode())));

        // 绑定社交用户
        socialUserService.bindSocialUser(socialUserDO.getId(), SocialTypeEnum.ZHL.getType(), user.getId(), getUserType().getValue());

        return ZhlBindAccountRespVO.builder().openid(socialUserDO.getOpenid()).build();
    }

    @Override
    public AppAuthLoginRespVO zhlTicketLogin(ZhlTicketLoginReqVO reqVO) {

        SocialUserRespDTO socialUserRespDTO = socialUserApi.getSocialUserByOpenid(reqVO.getOpenid(), SocialTypeEnum.ZHL.getType(), getUserType().getValue());
        if (socialUserRespDTO == null) {
            log.info("zhlTicketLogin社交用户不存在, openid: {}", reqVO.getOpenid());
            throw exception(ZHL_TICKET_LOGIN_ERROR);
        }
        MemberUserDO user = userService.getUser(socialUserRespDTO.getUserId());
        if (user == null) {
            log.info("zhlTicketLogin用户不存在, userId: {}", socialUserRespDTO.getUserId());
            throw exception(ZHL_TICKET_LOGIN_ERROR);
        }

        // 创建 Token 令牌，记录登录日志
        String clientId = getClientId(reqVO.getBusinessType(), reqVO.getClientType(), getUserType().getValue());
        return createTokenAfterLoginSuccess(user, user.getMobile(), LoginLogTypeEnum.LOGIN_SOCIAL, socialUserRespDTO.getOpenid(),clientId);
    }

    @Override
    public boolean zhlDeleteAccount(ZhlDeleteAccountReqVO reqVO) {

        SocialUserPageReqVO socialUserPageReqVO = new SocialUserPageReqVO();
        socialUserPageReqVO.setType(SocialTypeEnum.ZHL.getType());
        socialUserPageReqVO.setOpenidList(reqVO.getOpenidList());
        // 根据openid查询社交用户
        List<SocialUserDO> socialUserDOList = socialUserService.selectByCondition(socialUserPageReqVO);
        if (CollectionUtils.isEmpty(socialUserDOList)) {
            return true;
        }
        List<Long> socialUserIdList = socialUserDOList.stream().map(SocialUserDO::getId).filter(Objects::nonNull).toList();
        if (CollectionUtils.isEmpty(socialUserIdList)) {
            return true;
        }
        // 根据社交用户id集合查询关联会员的用户id
        List<SocialUserBindDO> socialUserBindDOList = socialUserService.selectBySocialUserIdAndUserTypeAndSocialType(socialUserIdList, getUserType().getValue(), SocialTypeEnum.ZHL.getType());
        if (CollectionUtils.isEmpty(socialUserBindDOList)) {
            return true;
        }
        List<Long> memberUserIdList = socialUserBindDOList.stream().map(SocialUserBindDO::getUserId).filter(Objects::nonNull).toList();

        // step 1 删除社交用户
        socialUserService.deleteSocialUserByIdList(socialUserIdList);
        // step 2 删除社交用户和会员关联关系
        socialUserService.deleteSocialUserBindBySocialUserIdListAndSocialType(socialUserIdList, SocialTypeEnum.ZHL.getType());
        // step 3 清除用户缓存
        adminAuthService.logout(memberUserIdList, UserTypeEnum.MEMBER, LoginLogTypeEnum.LOGIN_SOCIAL);

        return true;
    }
}
