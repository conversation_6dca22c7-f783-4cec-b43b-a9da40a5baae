package cn.iocoder.yudao.module.system.service.social;

import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.member.dal.mysql.user.MemberUserMapper;
import cn.iocoder.yudao.module.system.api.social.dto.SocialUserBindReqDTO;
import cn.iocoder.yudao.module.system.api.social.dto.SocialUserReqDTO;
import cn.iocoder.yudao.module.system.api.social.dto.SocialUserRespDTO;
import cn.iocoder.yudao.module.system.config.YbmWebConfig;
import cn.iocoder.yudao.module.system.controller.admin.socail.vo.user.SocialUserPageReqVO;
import cn.iocoder.yudao.module.system.convert.auth.SocialUserConvert;
import cn.iocoder.yudao.module.system.convert.tenant.TenantConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.social.SocialUserBindDO;
import cn.iocoder.yudao.module.system.dal.dataobject.social.SocialUserDO;
import cn.iocoder.yudao.module.system.dal.mysql.social.SocialUserBindMapper;
import cn.iocoder.yudao.module.system.dal.mysql.social.SocialUserMapper;
import cn.iocoder.yudao.module.system.enums.social.SocialTypeEnum;
import cn.iocoder.yudao.module.system.service.tenant.ybm.YbmWebClient;
import cn.iocoder.yudao.module.system.service.tenant.ybm.dto.YbmAccountDto;
import com.alibaba.fastjson.JSONObject;
import com.xingyuv.jushauth.model.AuthUser;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;
import static cn.iocoder.yudao.framework.common.util.json.JsonUtils.toJsonString;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.SOCIAL_USER_NOT_FOUND;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.YBM_USER_NOT_FOUND;

/**
 * 社交用户 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class SocialUserServiceImpl implements SocialUserService {

    @Resource
    private SocialUserBindMapper socialUserBindMapper;
    @Resource
    private SocialUserMapper socialUserMapper;

    @Resource
    private MemberUserMapper memberUserMapper;

    @Resource
    private SocialClientService socialClientService;

    @Resource
    private YbmWebClient ybmWebClient;

    @Resource
    private YbmWebConfig ybmWebConfig;

    @Override
    public List<SocialUserDO> getSocialUserList(Long userId, Integer userType) {
        // 获得绑定
        List<SocialUserBindDO> socialUserBinds = socialUserBindMapper.selectListByUserIdAndUserType(userId, userType);
        if (CollUtil.isEmpty(socialUserBinds)) {
            return Collections.emptyList();
        }
        // 获得社交用户
        return socialUserMapper.selectBatchIds(convertSet(socialUserBinds, SocialUserBindDO::getSocialUserId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String bindSocialUser(SocialUserBindReqDTO reqDTO) {
        // 获得社交用户
        SocialUserDO socialUser = authSocialUser(reqDTO.getSocialType(),reqDTO.getClientType(), reqDTO.getUserType(), reqDTO.getBusinessType(),
                reqDTO.getCode(), reqDTO.getState());
        Assert.notNull(socialUser, "社交用户不能为空");

        // 社交用户可能之前绑定过别的用户，需要进行解绑
        socialUserBindMapper.deleteByUserTypeAndSocialUserId(reqDTO.getUserType(), socialUser.getId());

        // 用户可能之前已经绑定过该社交类型，需要进行解绑
        socialUserBindMapper.deleteByUserTypeAndUserIdAndSocialType(reqDTO.getUserType(), reqDTO.getUserId(),
                socialUser.getType());

        // 绑定当前登录的社交用户
        SocialUserBindDO socialUserBind = SocialUserBindDO.builder()
                .userId(reqDTO.getUserId()).userType(reqDTO.getUserType())
                .socialUserId(socialUser.getId()).socialType(socialUser.getType()).build();
        socialUserBindMapper.insert(socialUserBind);
        return socialUser.getOpenid();
    }

    @Override
    public void unbindSocialUser(Long userId, Integer userType, Integer socialType, String openid) {
        // 获得 openid 对应的 SocialUserDO 社交用户
        SocialUserDO socialUser = socialUserMapper.selectByTypeAndOpenid(socialType, openid);
        if (socialUser == null) {
            throw exception(SOCIAL_USER_NOT_FOUND);
        }

        // 获得对应的社交绑定关系
        socialUserBindMapper.deleteByUserTypeAndUserIdAndSocialType(userType, userId, socialUser.getType());
    }

    @Override
    public SocialUserRespDTO getSocialUserByUserId(Integer userType, Long userId, Integer socialType) {
        // 获得绑定用户
        SocialUserBindDO socialUserBind = socialUserBindMapper.selectByUserIdAndUserTypeAndSocialType(userId, userType, socialType);
        if (socialUserBind == null) {
            return null;
        }
        // 获得社交用户
        SocialUserDO socialUser = socialUserMapper.selectById(socialUserBind.getSocialUserId());
        Assert.notNull(socialUser, "社交用户不能为空");
        return new SocialUserRespDTO(socialUser.getOpenid(), socialUser.getNickname(), socialUser.getAvatar(),
                socialUserBind.getUserId(), socialUser.getCode());
    }

    @Override
    public SocialUserRespDTO getSocialUserByCode(Integer userType, Integer socialType, Integer clientType, Integer businessType, String code, String state) {
        // 获得社交用户
        SocialUserDO socialUser = authSocialUser(socialType,clientType, userType, businessType, code, state);
        Assert.notNull(socialUser, "社交用户不能为空");

        // 获得绑定用户
        SocialUserBindDO socialUserBind = socialUserBindMapper.selectByUserTypeAndSocialUserId(userType,
                socialUser.getId());
        return new SocialUserRespDTO(socialUser.getOpenid(), socialUser.getNickname(), socialUser.getAvatar(),
                socialUserBind != null ? socialUserBind.getUserId() : null, socialUser.getCode());
    }

    @Override
    public SocialUserRespDTO getSocialUserByUserId(Integer userType, Long userId, String mobile, Integer socialType, String state) {
        // 获得社交用户
        SocialUserDO socialUser = authSocialUserByUserId(userType, state, userId, mobile, socialType);
        Assert.notNull(socialUser, "社交用户不能为空");

        // 获得绑定用户
        SocialUserBindDO socialUserBind = socialUserBindMapper.selectByUserTypeAndSocialUserId(userType,
            socialUser.getId());
        return new SocialUserRespDTO(socialUser.getOpenid(), socialUser.getNickname(), socialUser.getAvatar(),
            socialUserBind != null ? socialUserBind.getUserId() : null, socialUser.getCode());
    }

    /**
     * 授权获得对应的社交用户
     * 如果授权失败，则会抛出 {@link ServiceException} 异常
     *
     * @param socialType 社交平台的类型 {@link SocialTypeEnum}
     * @param userType 用户类型
     * @param code     授权码
     * @param state    state
     * @return 授权用户
     */
    @NotNull
    public SocialUserDO authSocialUser(Integer socialType, Integer userType, String code, String state) {
        return authSocialUser(socialType,null, userType, null, code, state);
    }

    /**
     * 授权获得对应的社交用户
     * 如果授权失败，则会抛出 {@link ServiceException} 异常
     *
     * @param socialType   社交平台的类型 {@link SocialTypeEnum}
     * @param clientType   客户端类型 {@link com.xyy.saas.inquiry.enums.system.ClientTypeEnum}
     * @param userType     用户类型
     * @param businessType 应用类型 {@link com.xyy.saas.inquiry.enums.system.BizTypeEnum}
     * @param code         授权码
     * @param state        state
     * @return 授权用户
     */
    @NotNull
    public SocialUserDO authSocialUser(Integer socialType, Integer clientType, Integer userType, Integer businessType, String code, String state) {
        // 优先从 DB 中获取，因为 code 有且可以使用一次。
        // 在社交登录时，当未绑定 User 时，需要绑定登录，此时需要 code 使用两次
        SocialUserDO socialUser = socialUserMapper.selectByTypeAndCodeAnState(socialType, code, state);
        if (socialUser != null) {
            return socialUser;
        }

        // 请求获取
        AuthUser authUser = socialClientService.getAuthUser(socialType, clientType, userType, businessType, code, state);
        Assert.notNull(authUser, "三方用户不能为空");

        // 保存到 DB 中
        socialUser = socialUserMapper.selectByTypeAndOpenid(socialType, authUser.getUuid());
        if (socialUser == null) {
            socialUser = new SocialUserDO();
        }
        socialUser.setType(socialType).setCode(code).setState(state) // 需要保存 code + state 字段，保证后续可查询
                .setOpenid(authUser.getUuid()).setToken(authUser.getToken().getAccessToken()).setRawTokenInfo((toJsonString(authUser.getToken())))
                .setNickname(authUser.getNickname()).setAvatar(authUser.getAvatar()).setRawUserInfo(toJsonString(authUser.getRawUserInfo()));
        if (socialUser.getId() == null) {
            socialUserMapper.insert(socialUser);
        } else {
            socialUserMapper.updateById(socialUser);
        }
        return socialUser;
    }

    /**
     * 授权获得微信小程序社交用户手机号
     *
     * @param userType     用户类型
     * @param businessType 业务类型
     * @param phoneCode    手机号授权码
     * @return 授权用户
     */
    @Override
    public String getWXSocialUserMobile(Integer userType, Integer businessType, String phoneCode) {

        // 请求获取
        WxMaPhoneNumberInfo authUserMobile = socialClientService.getWxMaPhoneNumberInfo(userType, businessType, phoneCode);
        Assert.notNull(authUserMobile, "三方用户手机号为空");

        return authUserMobile.getPhoneNumber();
    }



    /**
     * 授权获得对应的社交用户
     * 如果授权失败，则会抛出 {@link ServiceException} 异常
     *
     * @param userType   用户类型
     * @param state      状态
     * @param userId     用户id
     * @param mobile     手机号
     * @param socialType 社交平台的类型 {@link SocialTypeEnum}
     * @return 授权用户
     */
    @NotNull
    public SocialUserDO authSocialUserByUserId(Integer userType, String state, Long userId, String mobile, Integer socialType) {

        // 获得绑定用户
        SocialUserBindDO socialUserBind = null;
        if(userId != null){
            socialUserBind = socialUserBindMapper.selectByUserIdAndUserTypeAndSocialType(userId, userType, socialType);
        }
        SocialUserDO socialUser;

        //查询药帮忙用户信息
        YbmAccountDto ybmAccountDto = fetchYbmAccount(mobile);
        if(ybmAccountDto == null){
            throw exception(YBM_USER_NOT_FOUND);
        }

        // 获取社交用户信息
        if (socialUserBind != null) {
            socialUser = socialUserMapper.selectById(socialUserBind.getSocialUserId());
        }else{
            socialUser = new SocialUserDO();
            String specialCode = Optional.ofNullable(ybmAccountDto.getId())
                .map(id -> id + "-" + System.currentTimeMillis())
                .orElseGet(() -> String.valueOf(System.currentTimeMillis()));
            socialUser.setType(socialType).setCode(specialCode).setState(state) // 需要保存 code + state 字段，保证后续可查询
                .setOpenid(ybmAccountDto.getId().toString()).setToken(specialCode).setRawTokenInfo(toJsonString(new JSONObject()))
                .setNickname(ybmAccountDto.getContactName()).setAvatar("").setRawUserInfo(toJsonString(new JSONObject()));
        }

        // 保存到 DB 中
        if (socialUser.getId() == null) {
            socialUserMapper.insert(socialUser);
        } else {
            socialUserMapper.updateById(socialUser);
        }
        return socialUser;
    }

    /**
     * 更新已绑定的社交用户
     * 如果更新失败，则会抛出 {@link ServiceException} 异常
     *
     * @param userType     用户类型
     * @param state        状态
     * @param sourceUserId 源用户id
     * @param targetUserId 目标用户id
     * @param socialType 社交平台的类型 {@link SocialTypeEnum}
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBoundSocialUser(Integer userType, String state, Long sourceUserId, Long targetUserId, Integer socialType) {

        //获取社交用户绑定信息
        SocialUserBindDO sourceUserBind = socialUserBindMapper.selectByUserIdAndUserTypeAndSocialType(sourceUserId, userType, socialType);
        Assert.notNull(sourceUserBind, "源用户绑定信息不能为空");
        SocialUserDO sourceUser = socialUserMapper.selectById(sourceUserBind.getSocialUserId());
        Assert.notNull(sourceUserBind, "源用户信息不能为空");


        //获取原社交用户信息
        SocialUserBindDO targetUserBind = socialUserBindMapper.selectByUserIdAndUserTypeAndSocialType(targetUserId, userType, socialType);
        Assert.notNull(targetUserBind, "目标用户绑定信息不能为空");
        SocialUserDO targetUser = socialUserMapper.selectById(targetUserBind.getSocialUserId());
        Assert.notNull(sourceUserBind, "目标用户信息不能为空");


        //更新用户信息
        targetUser.setType(socialType).setCode(sourceUser.getCode()).setState(state) // 需要保存 code + state 字段，保证后续可查询
            .setOpenid(sourceUser.getOpenid()).setToken(sourceUser.getToken()).setRawTokenInfo(sourceUser.getRawTokenInfo())
            .setNickname(sourceUser.getNickname()).setAvatar(sourceUser.getAvatar()).setRawUserInfo(sourceUser.getRawUserInfo());
        socialUserMapper.updateById(targetUser);
        socialUserMapper.deleteById(sourceUser.getId());
        socialUserBindMapper.deleteById(sourceUserBind.getId());

    }

    /**
     * 从YBM系统获取账号信息
     * 调用外部YBM系统API，获取指定手机号关联的账号信息
     *
     * @param mobile 用户手机号
     * @return 账号信息，如果无数据则返回空
     */
    private YbmAccountDto fetchYbmAccount(String mobile) {
        // 构建查询参数
        Map<String, Object> params = TenantConvert.INSTANCE.buildQueryYbmMerchantReq(mobile);

        // 调用EC系统API
        String response = ybmWebClient.callApi(ybmWebConfig.getApi().getGetAccount(), params);

        // 解析响应数据
        com.alibaba.fastjson2.JSONObject jsonParam = com.alibaba.fastjson2.JSONObject.parseObject(response);
        if (jsonParam.get("data") == null) {
            return null;
        }

        // 转换为业务对象
        return JsonUtils.parseObject(JsonUtils.toJsonString(jsonParam.get("data")), YbmAccountDto.class);
    }

    // ==================== 社交用户 CRUD ====================

    @Override
    public SocialUserDO getSocialUser(Long id) {
        return socialUserMapper.selectById(id);
    }

    @Override
    public PageResult<SocialUserDO> getSocialUserPage(SocialUserPageReqVO pageReqVO) {
        return socialUserMapper.selectPage(pageReqVO);
    }

    @Override
    public SocialUserRespDTO getSocialUserByOpenid(String openid, Integer socialType, Integer userType) {
        SocialUserDO socialUserDO = socialUserMapper.selectByTypeAndOpenid(socialType, openid);
        if (socialUserDO == null) {
            return null;
        }
        // 获得绑定用户
        SocialUserBindDO socialUserBind = socialUserBindMapper.selectByUserTypeAndSocialUserId(userType,
            socialUserDO.getId());
        return new SocialUserRespDTO(socialUserDO.getOpenid(), socialUserDO.getNickname(), socialUserDO.getAvatar(),
            socialUserBind != null ? socialUserBind.getUserId() : null, socialUserDO.getCode());
    }

    @Override
    public SocialUserDO createSocialUser(SocialUserReqDTO socialUserReqDTO) {
        SocialUserDO socialUserDO = SocialUserConvert.INSTANCE.convertReqDTO2DO(socialUserReqDTO);
        socialUserMapper.insert(socialUserDO);
        return socialUserDO;
    }

    @Override
    public void bindSocialUser(Long socialUserId, Integer socialType, Long userId, Integer userType) {
        SocialUserBindDO socialUserBind = SocialUserBindDO.builder()
            .userId(userId).userType(userType)
            .socialUserId(socialUserId).socialType(socialType).build();
        socialUserBindMapper.insert(socialUserBind);
    }

    @Override
    public List<SocialUserDO> selectByCondition(SocialUserPageReqVO socialUserPageReqVO) {

        return socialUserMapper.selectByCondition(socialUserPageReqVO);
    }

    @Override
    public List<SocialUserBindDO> selectBySocialUserIdAndUserTypeAndSocialType(List<Long> socialUserIdList, Integer userType, Integer socialType) {
        return socialUserBindMapper.selectBySocialUserIdAndUserTypeAndSocialType(socialUserIdList, userType, socialType);
    }

    @Override
    public void deleteSocialUserByIdList(List<Long> socialUserIdList) {
        socialUserMapper.deleteByIds(socialUserIdList);
    }

    @Override
    public void deleteSocialUserBindBySocialUserIdListAndSocialType(List<Long> socialUserIdList, Integer socialType) {
        socialUserBindMapper.deleteBySocialUserIdListAndSocialType(socialUserIdList, socialType);
    }

}
