package com.xyy.saas.inquiry.im.server.controller.app.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 腾讯IM用户新增/修改 Request VO")
@Data
public class InquiryImUserSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "30810")
    private Long id;

    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "30000")
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @Schema(description = "im用户名,唯一", requiredMode = Schema.RequiredMode.REQUIRED, example = "12053")
    @NotEmpty(message = "im用户名,唯一不能为空")
    private String accountId;

    @Schema(description = "用户昵称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "用户昵称不能为空")
    private String nickName;

    @Schema(description = "用户类型 0-门店  1-医生", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "用户类型 0-门店  1-医生不能为空")
    private Integer userType;

    @Schema(description = "用户状态 0-正常 1-禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "用户状态 0-正常 1-禁用不能为空")
    private Integer userStatus;

}