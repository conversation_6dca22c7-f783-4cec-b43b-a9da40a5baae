package com.xyy.saas.inquiry.im.server.service.trtc;

import com.xyy.saas.inquiry.enums.inquiry.TranscodingStatusEnum;
import com.xyy.saas.inquiry.im.server.dal.dataobject.tencent.out.TencentTrtcBaseRespDO;
import com.xyy.saas.inquiry.im.server.dal.dataobject.trtc.InquiryTranscodingDO;

/**
 * @Author: xucao
 * @Date: 2024/12/11 14:22
 * @Description: 视频转码服务类
 */
public interface InquiryTranscodingService {

    /**
     * 添加转码任务
     * @param inquiryTranscodingDO 转码任务
     * @return 添加结果
     */
    Boolean addTranscodingRecord(InquiryTranscodingDO inquiryTranscodingDO);

    /**
     * 根据问诊pref查询转码任务
     * @param inquiryPref 问诊pref
     * @return 转码任务
     */
    InquiryTranscodingDO queryByInquiryPref(String inquiryPref);

    /**
     * 更新转码任务
     * @param transcodingDO 任务对象
     */
    void updateTranscodingRecord(InquiryTranscodingDO transcodingDO, TencentTrtcBaseRespDO respDO, TranscodingStatusEnum status);

    /**
     * 根据问诊单号删除转码任务
     * @param inquiryPref 问诊pref
     */
    void deleteByInquiryPref(String inquiryPref);
}
