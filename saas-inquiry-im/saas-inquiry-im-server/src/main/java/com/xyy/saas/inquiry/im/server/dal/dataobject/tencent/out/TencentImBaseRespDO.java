package com.xyy.saas.inquiry.im.server.dal.dataobject.tencent.out;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName：TencentImBaseRespDO
 * @Author: xucao
 * @Date: 2024/11/27 19:18
 * @Description: 必须描述类做什么事情, 实现什么功能
 */
@Data
public class TencentImBaseRespDO implements Serializable {
    /**
     * 请求处理的结果，OK 表示处理成功，FAIL 表示失败
     */
    private String ActionStatus;
    /**
     * 错误码，0表示成功，非0表示失败
     */
    private Integer ErrorCode;
    /**
     * 错误信息
     */
    private String ErrorInfo;

    public Boolean isSuccess() {
        return "OK".equals(ActionStatus);
    }
}
