package com.xyy.saas.inquiry.im.server.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @ClassName：BaseConfig
 * @Author: xucao
 * @Date: 2024/11/27 19:11
 * @Description: 通用基础配置类
 */
@Data
@Component
@ConfigurationProperties(prefix = "base-config")
public class BaseConfig {

    /**
     * HTTP 请求超时时间，单位：毫秒
     */
    private int httpTimeout;

}
