package com.xyy.saas.inquiry.im.server.service.trtc;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.im.enums.ErrorCodeConstants.INQUIRY_TRTC_TRANSCODING_ERROR;
import static com.xyy.saas.inquiry.im.enums.ErrorCodeConstants.INQUIRY_TRTC_TRANSCODING_RECORD_NOT_EXISTS;

import com.xyy.saas.inquiry.enums.inquiry.TranscodingStatusEnum;
import com.xyy.saas.inquiry.im.server.dal.dataobject.tencent.out.TencentTrtcBaseRespDO;
import com.xyy.saas.inquiry.im.server.dal.dataobject.trtc.InquiryTranscodingDO;
import com.xyy.saas.inquiry.im.server.dal.mysql.trtc.InquiryTranscodingMapper;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * @Author: xucao
 * @Date: 2024/12/11 14:35
 * @Description: 视频转码服务实现类
 */
@Service
public class InquiryTranscodingServiceImpl implements InquiryTranscodingService {

    @Resource
    private InquiryTranscodingMapper inquiryTranscodingMapper;

    /**
     * 添加转码任务
     *
     * @param inquiryTranscodingDO 转码任务
     * @return 添加结果
     */
    @Override
    public Boolean addTranscodingRecord(InquiryTranscodingDO inquiryTranscodingDO) {
        return inquiryTranscodingMapper.insert(inquiryTranscodingDO) > 0;
    }

    /**
     * 根据问诊pref查询转码任务
     *
     * @param inquiryPref 问诊pref
     * @return 转码任务
     */
    @Override
    public InquiryTranscodingDO queryByInquiryPref(String inquiryPref) {
        InquiryTranscodingDO inquiryTranscodingDO = inquiryTranscodingMapper.selectOne(InquiryTranscodingDO::getInquiryPref, inquiryPref);
        if(ObjectUtils.isEmpty(inquiryTranscodingDO)){
            throw exception(INQUIRY_TRTC_TRANSCODING_RECORD_NOT_EXISTS);
        }
        return inquiryTranscodingDO;
    }

    /**
     * 更新转码任务
     *
     * @param transcodingDO 任务对象
     * @param respDO 腾讯云返回结果
     * @param status 状态
     */
    @Override
    public void updateTranscodingRecord(InquiryTranscodingDO transcodingDO, TencentTrtcBaseRespDO respDO, TranscodingStatusEnum status) {
        //更新记录信息
        inquiryTranscodingMapper.updateById(InquiryTranscodingDO.builder().
            id(transcodingDO.getId()).
            inquiryPref(transcodingDO.getInquiryPref()).
            status(status.getCode()).
            taskId(respDO.getTaskId()).
            errorInfo(respDO.getErrorInfo()).build());
        //无异常情况下直接返回
        if(StringUtils.isBlank(respDO.getErrorInfo())){
            return;
        }
        //有异常情况下mq重试
        throw exception(INQUIRY_TRTC_TRANSCODING_ERROR);
    }

    /**
     * 根据问诊单号删除转码任务
     *
     * @param inquiryPref 问诊pref
     */
    @Override
    public void deleteByInquiryPref(String inquiryPref) {
        inquiryTranscodingMapper.deleteByInquiryPref(inquiryPref);
    }

}
