package com.xyy.saas.inquiry.im.server.dal.mysql.user;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.im.server.controller.app.message.vo.InquiryImMessagePageReqVO;
import com.xyy.saas.inquiry.im.server.controller.app.user.vo.InquiryImUserPageReqVO;
import com.xyy.saas.inquiry.im.server.dal.dataobject.user.InquiryImUserDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 腾讯IM用户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryImUserMapper extends BaseMapperX<InquiryImUserDO> {

    default PageResult<InquiryImUserDO> selectPage(InquiryImUserPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InquiryImUserDO>()
                .eqIfPresent(InquiryImUserDO::getUserId, reqVO.getUserId())
                .eqIfPresent(InquiryImUserDO::getAccountId, reqVO.getAccountId())
                .likeIfPresent(InquiryImUserDO::getNickName, reqVO.getNickName())
                .eqIfPresent(InquiryImUserDO::getClientType, reqVO.getClientType())
                .eqIfPresent(InquiryImUserDO::getUserStatus, reqVO.getUserStatus())
                .betweenIfPresent(InquiryImUserDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InquiryImUserDO::getId));
    }

    default List<InquiryImUserDO> selectList(InquiryImMessagePageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<InquiryImUserDO>()
                .inIfPresent(InquiryImUserDO::getAccountId, reqVO.getAccountList())
            .inIfPresent(InquiryImUserDO::getUserId, reqVO.getUserIdList())
            .eqIfPresent(InquiryImUserDO::getClientType, reqVO.getClientChannelType())
        );
    }

}