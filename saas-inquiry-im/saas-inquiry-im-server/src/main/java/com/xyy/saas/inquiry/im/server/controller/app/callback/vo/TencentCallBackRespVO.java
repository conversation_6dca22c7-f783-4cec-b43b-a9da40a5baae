package com.xyy.saas.inquiry.im.server.controller.app.callback.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: xucao
 * @Date: 2024/11/28 17:34
 * @Description: 腾讯IM 回调响应请求响应VO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TencentCallBackRespVO {
    /**
     * 请求处理的结果，OK 表示处理成功，FAIL 表示失败
     */
    private String ActionStatus;
    /**
     * 错误码，0表示成功，非0表示失败
     */
    private Integer ErrorCode;
    /**
     * 错误信息
     */
    private String ErrorInfo;


    public static TencentCallBackRespVO callBackSuccess() {
        return TencentCallBackRespVO.builder()
                .ActionStatus("OK")
                .ErrorCode(0)
                .ErrorInfo("成功")
                .build();
    }
}
