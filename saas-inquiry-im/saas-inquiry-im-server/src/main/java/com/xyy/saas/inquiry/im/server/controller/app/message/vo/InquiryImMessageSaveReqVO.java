package com.xyy.saas.inquiry.im.server.controller.app.message.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "管理后台 - 腾讯IM用户消息新增/修改 Request VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InquiryImMessageSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10081")
    private Long id;

    @Schema(description = "消息唯一key", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "消息唯一key不能为空")
    private String msgKey;

    @Schema(description = "消息序列号，同一秒的消息,msgSeq 值越大消息越靠后", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "消息序列号")
    private Long msgSeq;

    @Schema(description = "问诊单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "问诊单号不能为空")
    private String inquiryPref;

    @Schema(description = "发送方IM用户名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "发送方IM用户名不能为空")
    private String msgFrom;

    @Schema(description = "接收方IM用户名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "接收方IM用户名不能为空")
    private String msgTo;

    @Schema(description = "消息发送时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "消息发送时间不能为空")
    private LocalDateTime msgTime;

    @Schema(description = "消息已读状态 0-未读 1-已读", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "消息已读状态 0-未读 1-已读不能为空")
    private Integer readStatus;

    @Schema(description = "消息体内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "消息体内容不能为空")
    private String msgBody;

    @Schema(description = "消息扩展内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "消息扩展内容不能为空")
    private String msgExt;

}