package com.xyy.saas.inquiry.im.server.dal.dataobject.user;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 腾讯IM用户 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_im_user")
@KeySequence("saas_inquiry_im_user_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryImUserDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * im用户名,唯一
     */
    private String accountId;
    /**
     * 用户昵称
     */
    private String nickName;
    /**
     * 客户端类型 0-app  1-pc  2 - wechat
     */
    private Integer clientType;
    /**
     * 最后一次使用设备类型 1-ios  2-android  3-harmony
     */
    private Integer lastDeviceType;
    /**
     * 用户状态 0-正常 1-禁用
     */
    private Integer userStatus;

}