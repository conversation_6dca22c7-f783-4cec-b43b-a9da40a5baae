package com.xyy.saas.inquiry.im.server.convert.message;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.xyy.saas.inquiry.enums.im.ImSourceTypeEnum;
import com.xyy.saas.inquiry.enums.im.ImSourceTypeEnum.ImChatAnalysisObj;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionDateTypeEnum;
import com.xyy.saas.inquiry.enums.tencent.TencentImMessageTypeEnum;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryLastMessageDto;
import com.xyy.saas.inquiry.im.server.controller.app.callback.vo.TencentImCallBackReqVO;
import com.xyy.saas.inquiry.im.server.controller.app.callback.vo.TencentImCallBackReqVO.MsgExt;
import com.xyy.saas.inquiry.im.server.controller.app.message.vo.InquiryImMessageRespVO;
import com.xyy.saas.inquiry.im.server.controller.app.message.vo.InquiryImMessageSaveReqVO;
import com.xyy.saas.inquiry.im.server.dal.dataobject.message.InquiryImArchiveMessageDO;
import com.xyy.saas.inquiry.im.server.dal.dataobject.message.InquiryImMessageDO;
import com.xyy.saas.inquiry.im.server.dal.dataobject.tencent.in.TencentImBatchMessageDO;
import com.xyy.saas.inquiry.im.server.dal.dataobject.tencent.in.TencentImSendMessageDO;
import com.xyy.saas.inquiry.im.server.dal.dataobject.tencent.in.TencentImSendMessageDO.MsgBody;
import com.xyy.saas.inquiry.signature.api.immessage.dto.InquiryImMessageDto;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAccessor;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: xucao
 * @Date: 2024/12/02 13:42
 * @Description: IM 消息转换器
 */
@Mapper
public interface InquiryImMessageConvert {

    InquiryImMessageConvert INSTANCE = Mappers.getMapper(InquiryImMessageConvert.class);

    /**
     * 将外部VO对象转为内部创建对象的vo
     *
     * @param callBackReqVO 回调对象
     * @return 返回内部创建对象
     */
    default InquiryImMessageSaveReqVO converOutVO2VO(TencentImCallBackReqVO callBackReqVO) {
        return InquiryImMessageSaveReqVO.builder().msgKey(callBackReqVO.getMsgKey()).msgSeq(callBackReqVO.getMsgSeq()).inquiryPref(JSON.parseObject(callBackReqVO.getCloudCustomData(), TencentImCallBackReqVO.MsgExt.class).getInquiryPref())
            .msgFrom(callBackReqVO.getFromAccount()).msgTo(callBackReqVO.getToAccount()).msgTime(callBackReqVO.getMsgTime()).msgBody(JSON.toJSONString(callBackReqVO.getMsgBody().getFirst()))
            .msgExt(JSON.toJSONString(callBackReqVO.getCloudCustomData())).build();
    }

    List<InquiryImMessageRespVO> converMsgDOLIST2VOLIST(List<InquiryImMessageDO> msgList);

    /**
     * 将聊天记录转为生成PDF文件的聊天列表，并反转列表顺序（因为查询出来的排序是时间降序，但是生成文件时需要时间升序）
     *
     * @param voList
     * @return
     */
    default List<InquiryImMessageDto> converMsgVOLIST2DTOLIST(List<InquiryImMessageRespVO> voList, Integer dateType) {

        List<InquiryImMessageDto> result = new ArrayList<>();

        for (InquiryImMessageRespVO item : voList) {

            String msgExtStandardJsonStr = item.getMsgExt().replace("\\\"", "\"").replaceAll("^\"|\"$", "");
            MsgExt msgExt = JSON.parseObject(msgExtStandardJsonStr, MsgExt.class);

            if (!ImSourceTypeEnum.getShowInImPdfByCode(msgExt.getSourceType())) {
                continue;
            }

            InquiryImMessageDto inquiryImMessageDto = new InquiryImMessageDto();
            inquiryImMessageDto.setUserName(item.getFromUserName());
            inquiryImMessageDto.setMsgTime(PrescriptionDateTypeEnum.formatDate(dateType, item.getMsgTime()));
            inquiryImMessageDto.setSourceType(msgExt.getSourceType());
            ImChatAnalysisObj imChatAnalysisObj = ImChatAnalysisObj.builder()
                .sourceType(msgExt.getSourceType())
                .msgBodyJsonStr(item.getMsgBody())
                .msgExtJsonStr(msgExtStandardJsonStr).build();
            inquiryImMessageDto.setMsgContent(ImSourceTypeEnum.getImPdfContentByCode(imChatAnalysisObj));

            result.add(inquiryImMessageDto);
        }

        Collections.reverse(result);
        return result;
    }

    default Long getMsgSeq() {
        // 获取系统默认时区
        ZoneId zone = ZoneId.systemDefault();
        // 计算当前时间与当天零点的时间差（毫秒）
        return ZonedDateTime.now(zone).toInstant().toEpochMilli() - // 当前时间戳
            ZonedDateTime.now(zone).truncatedTo(ChronoUnit.DAYS).toInstant().toEpochMilli() // 当天零点时间戳
            ;
    }

    default Map<String, Object> buildCreateImMessageDO(com.xyy.saas.inquiry.im.api.message.dto.InquiryImMessageDto messageDto) {
        JSONObject ext = new JSONObject();
        ext.put("inquiryPref", messageDto.getInquiryPref());
        ext.put("sourceType", ImSourceTypeEnum.MsgText.getCode());
        ext.put("newInquiry", Boolean.TRUE);
        TencentImSendMessageDO imSendMessageDO = TencentImSendMessageDO.builder().fromAccount(messageDto.getFromAccount()).toAccount(messageDto.getToAccount()).msgSeq(getMsgSeq())
            .msgRandom(new Random(System.currentTimeMillis()).nextLong(**********))
            .msgBody(Arrays.asList(TencentImSendMessageDO.MsgBody.builder().msgType(TencentImMessageTypeEnum.TEXT.getType()).msgContent(TencentImSendMessageDO.MsgBody.MsgContent.builder().text(messageDto.getMsg()).build()).build()))
            .cloudCustomData(JSONObject.toJSONString(ObjectUtil.isEmpty(messageDto.getExtDto()) ? ext : messageDto.getExtDto())).isNeedReadReceipt(1).build();
        return JSON.parseObject(JSON.toJSONString(imSendMessageDO), Map.class);
    }

    default Map<String, Object> buildSystemMessageDO(com.xyy.saas.inquiry.im.api.message.dto.InquiryImMessageDto messageDto) {
        TencentImSendMessageDO imSendMessageDO = TencentImSendMessageDO.builder().syncOtherMachine(2).toAccount(messageDto.getToAccount()).msgSeq(getMsgSeq())
            .msgRandom(new Random(System.currentTimeMillis()).nextLong(**********))
            // 禁止回调控制选项
            .forbidCallbackControl(Arrays.asList("ForbidBeforeSendMsgCallback", "ForbidAfterSendMsgCallback"))
            .msgBody(Arrays.asList(TencentImSendMessageDO.MsgBody.builder().msgType(TencentImMessageTypeEnum.CUSTOM.getType()).msgContent(TencentImSendMessageDO.MsgBody.MsgContent.builder().text(messageDto.getMsg()).build()).build()))
            .cloudCustomData(JSONObject.toJSONString(messageDto.getExtDto())).build();
        return JSON.parseObject(JSON.toJSONString(imSendMessageDO), Map.class);
    }

    default Map<String, Object> buildSystemBatchMessageDO(com.xyy.saas.inquiry.im.api.message.dto.InquiryImMessageDto messageDto) {
        TencentImBatchMessageDO batchMessageDO = TencentImBatchMessageDO.builder().syncOtherMachine(2).toAccount(messageDto.getToAccountList()).msgSeq(getMsgSeq())
            .msgRandom(new Random(System.currentTimeMillis()).nextLong(**********))
            // 禁止回调控制选项
            .forbidCallbackControl(Arrays.asList("ForbidBeforeSendMsgCallback", "ForbidAfterSendMsgCallback"))
            .msgBody(Arrays.asList(TencentImSendMessageDO.MsgBody.builder().msgType(TencentImMessageTypeEnum.CUSTOM.getType()).msgContent(TencentImSendMessageDO.MsgBody.MsgContent.builder().text(messageDto.getMsg()).build()).build()))
            .cloudCustomData(JSONObject.toJSONString(messageDto.getExtDto())).build();
        return JSON.parseObject(JSON.toJSONString(batchMessageDO), Map.class);
    }

    default List<InquiryImMessageDO> convertDTOList2DOList(List<com.xyy.saas.inquiry.im.api.message.dto.InquiryImMessageDto> messageDtos) {
        return messageDtos.stream().map(this::convertDTO2DO).toList();
    }

    default InquiryImMessageDO convertDTO2DO(com.xyy.saas.inquiry.im.api.message.dto.InquiryImMessageDto dto) {
        // 定义消息体
        JSONObject msgBody = new JSONObject();
        JSONObject msgContent = new JSONObject();
        msgBody.put("msgType", StringUtils.isBlank(dto.getMsg()) ? TencentImMessageTypeEnum.CUSTOM.getType() : TencentImMessageTypeEnum.TEXT.getType());
        msgContent.put("text", dto.getMsg());
        msgBody.put("msgContent", msgContent);
        // 定义扩展消息
        JSONObject ext = new JSONObject();
        ext.put("inquiryPref", dto.getInquiryPref());
        ext.put("sourceType", ImSourceTypeEnum.MsgText.getCode());
        ext.put("newInquiry", Boolean.TRUE);
        if (ObjectUtil.isNotEmpty(dto.getExtDto())) {
            ext = JSONObject.parseObject(JSON.toJSONString(dto.getExtDto()));
        }
        String jsonExt = ext.toString().replace("\"", "\\\"");
        return InquiryImMessageDO.builder().msgKey(UUID.randomUUID().toString()).msgSeq(getMsgSeq()).inquiryPref(dto.getInquiryPref()).msgFrom(dto.getFromAccount()).msgTo(dto.getToAccount()).msgTime(dto.getSendTime()).readOffset(1)
            .msgBody(JSONObject.toJSONString(msgBody)).msgExt("\"" + jsonExt + "\"").build();
    }


    default InquiryImMessageDO convertVO2DOWithUser(InquiryImMessageSaveReqVO createReqVO) {
        InquiryImMessageDO inquiryImMessage = convertVO2DO(createReqVO);
        inquiryImMessage.setCreator("");
        inquiryImMessage.setUpdater("");
        inquiryImMessage.setCreateTime(LocalDateTimeUtil.now());
        inquiryImMessage.setUpdateTime(LocalDateTimeUtil.now());
        return inquiryImMessage;
    }

    InquiryImMessageDO convertVO2DO(InquiryImMessageSaveReqVO createReqVO);

    default InquiryLastMessageDto convertDTO(InquiryLastMessageDto inquiryLastMessageDto) {
        MsgBody msgBody = JSON.parseObject(inquiryLastMessageDto.getLastMsg(), TencentImSendMessageDO.MsgBody.class);
        String sourceType = JSON.parseObject(inquiryLastMessageDto.getMsgExt().replace("\\\"", "\"").replaceAll("^\"|\"$", "")).getOrDefault("sourceType", ImSourceTypeEnum.MsgText.getCode()).toString();
        return InquiryLastMessageDto.builder().lastMsg(StringUtils.equals(sourceType, ImSourceTypeEnum.MsgText.getCode()) ? msgBody.getMsgContent().getText() : ImSourceTypeEnum.getDescriptionByCode(sourceType))
            .inquiryPref(inquiryLastMessageDto.getInquiryPref()).build();
    }

    InquiryImArchiveMessageDO convertDO2ArchiveDO(InquiryImMessageDO inquiryImMessageDO);

    default void convertMsgTime(InquiryImMessageRespVO message, Integer dateType) {

        try {
            // 解析 msgExt JSON 字符串
            String msgExt = message.getMsgExt();
            if (StringUtils.isNotBlank(msgExt)) {
                // 移除多余的转义字符
                String cleanJson = msgExt.replaceAll("^\"|\"$", "").replace("\\\"", "\"");

                // 解析 JSON
                ObjectMapper mapper = new ObjectMapper();
                JsonNode rootNode = mapper.readTree(cleanJson);

                // 处理 notifyTime
                if (rootNode.has("notifyInfo") && rootNode.get("notifyInfo").has("notifyTime")) {
                    String notifyTime = rootNode.get("notifyInfo").get("notifyTime").asText();
                    String formatted = formatDateTime(notifyTime, PrescriptionDateTypeEnum.getDateformat(dateType));
                    ((ObjectNode) rootNode.get("notifyInfo")).put("notifyTimeStr", formatted);
                }

                // 处理 outPrescriptionTime
                if (rootNode.has("prescriptionInfo") && rootNode.get("prescriptionInfo").has("outPrescriptionTime")) {
                    String outTime = rootNode.get("prescriptionInfo").get("outPrescriptionTime").asText();
                    String formatted = formatDateTime(outTime, PrescriptionDateTypeEnum.getDateformat(dateType));
                    ((ObjectNode) rootNode.get("prescriptionInfo")).put("outPrescriptionTimeStr", formatted);
                }

                // 处理 startTime
                if (rootNode.has("prescriptionInfo") && rootNode.get("prescriptionInfo").has("startTime")) {
                    String outTime = rootNode.get("prescriptionInfo").get("startTime").asText();
                    String formatted = formatDateTime(outTime, PrescriptionDateTypeEnum.getDateformat(dateType));
                    ((ObjectNode) rootNode.get("prescriptionInfo")).put("startTimeStr", formatted);
                }

                // 将修改后的 JSON 转换为字符串并重新添加转义
                String updatedJson = mapper.writeValueAsString(rootNode);
                // 重新添加转义，保持原始格式
                String escapedJson = updatedJson.replace("\"", "\\\"");

                // 如果原始字符串有外层双引号，则添加回去
                boolean hadOuterQuotes = msgExt.startsWith("\"") && msgExt.endsWith("\"");
                // 如果需要外层双引号则添加
                if (hadOuterQuotes) {
                    escapedJson = "\"" + escapedJson + "\"";
                }

                message.setMsgExt(escapedJson);
            }
        } catch (Exception e) {
        }
    }

    private String formatDateTime(String isoDateTime, String pattern) {
        if (StringUtils.isBlank(isoDateTime)) {
            return "";
        }

        try {
            // 解析 ISO 8601 格式
            TemporalAccessor temporal = DateTimeFormatter.ISO_DATE_TIME.parseBest(isoDateTime,
                Instant::from,
                LocalDateTime::from,
                LocalDate::from
            );

            // 根据类型格式化
            if (temporal instanceof Instant) {
                return DateTimeFormatter.ofPattern(pattern)
                    .withZone(ZoneId.systemDefault())
                    .format((Instant) temporal);
            } else if (temporal instanceof LocalDateTime) {
                return DateTimeFormatter.ofPattern(pattern)
                    .format((LocalDateTime) temporal);
            } else if (temporal instanceof LocalDate) {
                return DateTimeFormatter.ofPattern(pattern)
                    .format((LocalDate) temporal);
            }
        } catch (DateTimeParseException e) {
        }

        // 尝试简单截取处理
        if (isoDateTime.length() >= 19) {
            return isoDateTime.substring(0, 19).replace("T", " ");
        } else if (isoDateTime.length() >= 10) {
            return isoDateTime.substring(0, 10);
        }

        return isoDateTime;
    }
}
