package com.xyy.saas.inquiry.im.server.dal.mysql.message;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.enums.tencent.TencentImMessageReadStatusEnum;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryLastMessageDto;
import com.xyy.saas.inquiry.im.server.controller.app.message.vo.InquiryImMessagePageReqVO;
import com.xyy.saas.inquiry.im.server.dal.dataobject.message.InquiryImMessageDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 腾讯IM用户消息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryImMessageMapper extends BaseMapperX<InquiryImMessageDO> {

    default PageResult<InquiryImMessageDO> selectPage(InquiryImMessagePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InquiryImMessageDO>()
                .eqIfPresent(InquiryImMessageDO::getMsgKey, reqVO.getMsgKey())
                .eqIfPresent(InquiryImMessageDO::getInquiryPref, reqVO.getInquiryPref())
                .eqIfPresent(InquiryImMessageDO::getMsgFrom, reqVO.getMsgFrom())
                .eqIfPresent(InquiryImMessageDO::getMsgTo, reqVO.getMsgTo())
                .betweenIfPresent(InquiryImMessageDO::getMsgTime, reqVO.getMsgTime())
                .eqIfPresent(InquiryImMessageDO::getReadOffset, reqVO.getReadStatus())
                .eqIfPresent(InquiryImMessageDO::getMsgBody, reqVO.getMsgBody())
                .eqIfPresent(InquiryImMessageDO::getMsgExt, reqVO.getMsgExt())
                .betweenIfPresent(InquiryImMessageDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InquiryImMessageDO::getMsgTime)
                .orderByDesc(InquiryImMessageDO::getMsgSeq));
    }

    default List<InquiryImMessageDO> selectList(InquiryImMessagePageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<InquiryImMessageDO>()
                .eqIfPresent(InquiryImMessageDO::getInquiryPref, reqVO.getInquiryPref())
                .eqIfPresent(InquiryImMessageDO::getMsgTo, reqVO.getMsgTo())
                .eqIfPresent(InquiryImMessageDO::getMsgFrom, reqVO.getMsgFrom())
                .betweenIfPresent(InquiryImMessageDO::getMsgTime, reqVO.getMsgTime())
                .orderByDesc(InquiryImMessageDO::getMsgTime)
                .orderByDesc(InquiryImMessageDO::getMsgSeq));
    }

    default InquiryImMessageDO selectLastReadMsg(InquiryImMessageDO build){
        return selectOne(new LambdaQueryWrapperX<InquiryImMessageDO>()
                .eq(InquiryImMessageDO::getMsgTo, build.getMsgTo())
                .eq(InquiryImMessageDO::getMsgFrom, build.getMsgFrom())
                .le(InquiryImMessageDO::getMsgTime, build.getMsgTime())
                .eq(InquiryImMessageDO::getReadOffset, TencentImMessageReadStatusEnum.UNREAD.getCode())
                .orderByDesc(InquiryImMessageDO::getMsgTime)
                .orderByDesc(InquiryImMessageDO::getMsgSeq)
                .last("limit 1"));
    }

    void deleteByInquiryPref(String inquiryPref);

    void physicalDeleteByInquiryPrefList(@Param("inquiryPrefList") List<String> inquiryPrefList);

    List<InquiryLastMessageDto> selectLastMessageByInquiryPrefs(List<String> inquiryPrefs);
}