package com.xyy.saas.inquiry.im.server.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: xucao
 * @Date: 2024/12/10 14:23
 * @Description: 腾讯Trtc配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "tencent.trtc")
public class TencentTrtcConfig {

    /**
     * 云点播
     */
    private Vod vod;

    /**
     * 实时音视频
     */
    private Rav rav;

    /**
     * 云直播
     */
    private Live live;

    @Data
    public static class Vod {

        /**
         * 云点播接口请求域名
         */
        private String endPoint;

        /**
         * 云点播 api秘钥id
         */
        private String secretId;

        /**
         * 云点播 api秘钥key
         */
        private String secretKey;

        /**
         * 转码模板id
         */
        private Long transcodingTemplateId;
    }

    @Data
    public static class Rav {

        /**
         * 实时音视频 sdkAppId
         */
        private Long sdkAppId;

        /**
         * 实时音视频 userSignKey
         */
        private String userSignKey;

        /**
         * 实时音视频请求的域名
         */
        private String endPoint;

        /**
         * 区域
         */
        private String region;

        /**
         * 业务id
         */
        private String bizId;
    }

    @Data
    public static class Live {

        /**
         * 直播的域名
         */
        private String endPoint;

        /**
         * 直播推流域名
         */
        private String pushDomain;

        /**
         * 推流域名主key
         */
        private String apiKey;
    }
}
