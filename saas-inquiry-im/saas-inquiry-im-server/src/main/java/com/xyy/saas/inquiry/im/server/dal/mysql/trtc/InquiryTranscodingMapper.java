package com.xyy.saas.inquiry.im.server.dal.mysql.trtc;


import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.xyy.saas.inquiry.im.server.dal.dataobject.trtc.InquiryTranscodingDO;
import org.apache.ibatis.annotations.Mapper;


/**
 * 问诊视频转码记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryTranscodingMapper extends BaseMapperX<InquiryTranscodingDO> {

    void deleteByInquiryPref(String inquiryPref);
}