package com.xyy.saas.inquiry.im.server.controller.app.message;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryImMessageDto;
import com.xyy.saas.inquiry.im.server.controller.app.message.vo.InquiryImMessagePageReqVO;
import com.xyy.saas.inquiry.im.server.controller.app.message.vo.InquiryImMessageRespVO;
import com.xyy.saas.inquiry.im.server.controller.app.message.vo.InquiryImMessageSaveReqVO;
import com.xyy.saas.inquiry.im.server.dal.dataobject.message.InquiryImMessageDO;
import com.xyy.saas.inquiry.im.server.service.message.InquiryImMessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "APP - 腾讯IM用户消息")
@RestController
@RequestMapping(value = {"/admin-api/kernel/im/inquiry-im-message", "/app-api/kernel/im/inquiry-im-message"})
@Validated
public class InquiryImMessageController {

    @Resource
    private InquiryImMessageService inquiryImMessageService;

    @PostMapping("/create")
    @Operation(summary = "创建腾讯IM用户消息")
    public CommonResult<Long> createInquiryImMessage(@Valid @RequestBody InquiryImMessageSaveReqVO createReqVO) {
        return success(inquiryImMessageService.createInquiryImMessage(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新腾讯IM用户消息")
    public CommonResult<Boolean> updateInquiryImMessage(@Valid @RequestBody InquiryImMessageSaveReqVO updateReqVO) {
        inquiryImMessageService.updateInquiryImMessage(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除腾讯IM用户消息")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteInquiryImMessage(@RequestParam("id") Long id) {
        inquiryImMessageService.deleteInquiryImMessage(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得腾讯IM用户消息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<InquiryImMessageRespVO> getInquiryImMessage(@RequestParam("id") Long id) {
        InquiryImMessageDO inquiryImMessage = inquiryImMessageService.getInquiryImMessage(id);
        return success(BeanUtils.toBean(inquiryImMessage, InquiryImMessageRespVO.class));
    }

    @PostMapping("/sendMsg")
    @Operation(summary = "获得腾讯IM用户消息")
    public CommonResult<Boolean> sendMsg(@RequestBody InquiryImMessageDto messageDto) {
        return success(inquiryImMessageService.sendUserMessage(messageDto));
    }

    @GetMapping("/page")
    @Operation(summary = "获得腾讯IM用户消息分页")
    public CommonResult<PageResult<InquiryImMessageRespVO>> getInquiryImMessagePage(@Valid InquiryImMessagePageReqVO pageReqVO) {
        return success(inquiryImMessageService.getInquiryImMessagePage(pageReqVO));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出腾讯IM用户消息 Excel")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInquiryImMessageExcel(@Valid InquiryImMessagePageReqVO pageReqVO,
                                            HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InquiryImMessageRespVO> list = inquiryImMessageService.getInquiryImMessagePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "腾讯IM用户消息.xls", "数据", InquiryImMessageRespVO.class,
                list);
    }

}