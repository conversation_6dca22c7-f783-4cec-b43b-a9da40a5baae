package com.xyy.saas.inquiry.im.server.mq.message;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import com.xyy.saas.inquiry.im.server.mq.message.dto.TranscodingTaskMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: xucao
 * @Date: 2024/12/15 17:20
 * @Description: 视频转码任务结束事件
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class TranscodingTaskEndEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "TRANSCODING_TASK_END";

    private TranscodingTaskMessage msg;


    @JsonCreator
    public TranscodingTaskEndEvent(@JsonProperty("msg") TranscodingTaskMessage msg) {
        this.msg = msg;
    }

    /**
     * 消息tag
     */
    @Override
    public String getTag() {
        return "";
    }
}
