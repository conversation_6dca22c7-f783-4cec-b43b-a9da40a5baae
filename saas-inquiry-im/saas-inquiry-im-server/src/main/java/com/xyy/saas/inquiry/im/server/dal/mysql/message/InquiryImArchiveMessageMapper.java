package com.xyy.saas.inquiry.im.server.dal.mysql.message;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.xyy.saas.inquiry.im.server.dal.dataobject.message.InquiryImArchiveMessageDO;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;


/**
 * 腾讯IM用户消息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryImArchiveMessageMapper extends BaseMapperX<InquiryImArchiveMessageDO> {

    int batchInsertByXml(List<InquiryImArchiveMessageDO> list);

}