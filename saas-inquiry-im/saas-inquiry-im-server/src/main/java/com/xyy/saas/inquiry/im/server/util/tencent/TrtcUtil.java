package com.xyy.saas.inquiry.im.server.util.tencent;

import com.tencentyun.TLSSigAPIv2;
import com.xyy.saas.inquiry.im.server.config.TencentTrtcConfig;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName：TrtcUserSig
 * @Author: xucao
 * @Date: 2025/3/5 14:51
 * @Description: trtc 用户签名类
 */
@Component
public class TrtcUtil {

    private  final TencentTrtcConfig config;


    private static long ravSdkAppId;
    private static  String userSignKey;

    @Autowired
    public TrtcUtil(TencentTrtcConfig config) {
        this.config = config;
    }

    @PostConstruct
    public void init() {
        ravSdkAppId = this.config.getRav().getSdkAppId();
        userSignKey = this.config.getRav().getUserSignKey();
    }

    /**
     * 获取TRTC用户签名
     *
     * @param userId 用户id
     * @return 用户签名
     */
    public static String getSign(String userId) {
        TLSSigAPIv2 tlsSigApi = new TLSSigAPIv2(ravSdkAppId, userSignKey);
        return tlsSigApi.genUserSig(userId, 7 * 24 * 60 * 60);
    }

    /**
     * 获取TRTC sdkAppId
     * @return
     */
    public static Long getSdkAppId() {
        return ravSdkAppId;
    }
}