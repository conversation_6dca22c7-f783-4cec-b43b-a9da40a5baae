package com.xyy.saas.inquiry.im.server.controller.app.callback.vo;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.im.enums.ErrorCodeConstants.INQUIRY_TRTC_CALL_BACK_MESSAGE_ERROR;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import java.io.Serializable;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

/**
 * @Author: xucao
 * @Date: 2024/11/28 17:46
 * @Description: 腾讯IM回调请求入参
 */
@Data
@Slf4j
public class TencentTrtcCallBackReqVO implements Serializable {

    @JSONField(name = "EventGroupId")
    private String eventGroupId;

    @JSONField(name = "EventType")
    private Integer eventType;

    @JSONField(name = "CallbackTs")
    private String callbackTs;

    @JSONField(name = "EventInfo")
    private EventInfo eventInfo;

    @Data
    public static class EventInfo implements Serializable {

        @JSONField(name = "RoomId")
        private String roomId;

        @JSONField(name = "EventTs")
        private String eventTs;

        @JSONField(name = "EventMsTs")
        private String eventMsTs;

        @JSONField(name = "UserId")
        private String userId;

        @JSONField(name = "Role")
        private String role;

        @JSONField(name = "TerminalType")
        private String terminalType;

        @JSONField(name = "UserType")
        private String userType;

        @JSONField(name = "Reason")
        private String reason;
    }

    //事件参数校验
    public Boolean checkEvent() {
        if (ObjectUtils.isEmpty(eventInfo) || ObjectUtils.isEmpty(eventInfo.getRoomId()) || ObjectUtils.isEmpty(eventInfo.getUserId())) {
            log.info("tencent trtc room callBak message error,event:{}", JSON.toJSONString(eventInfo));
            throw exception(INQUIRY_TRTC_CALL_BACK_MESSAGE_ERROR);
        }
        return Boolean.TRUE;
    }

}
