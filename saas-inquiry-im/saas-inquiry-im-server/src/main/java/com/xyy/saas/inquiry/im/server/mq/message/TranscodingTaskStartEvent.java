package com.xyy.saas.inquiry.im.server.mq.message;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import com.xyy.saas.inquiry.im.server.mq.message.dto.TranscodingTaskMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: xucao
 * @Date: 2024/12/11 15:05
 * @Description: 视频转码任务开始事件
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class TranscodingTaskStartEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "TRANSCODING_TASK_START";

    private TranscodingTaskMessage msg;


    @JsonCreator
    public TranscodingTaskStartEvent(@JsonProperty("msg") TranscodingTaskMessage msg) {
        this.msg = msg;
    }

    @Override
    public String getTag() {
        return "";
    }
}
