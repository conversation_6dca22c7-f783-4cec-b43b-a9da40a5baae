package com.xyy.saas.inquiry.im.server.service.trtc.strategy.trtccallback;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.im.enums.ErrorCodeConstants.INQUIRY_TRTC_PUBLISH_STREAM_ERROR;
import static com.xyy.saas.inquiry.im.enums.ErrorCodeConstants.INQUIRY_TRTC_START_PUBLISH_STREAM_ERROR;

import com.xyy.saas.inquiry.enums.inquiry.StreamStatus;
import com.xyy.saas.inquiry.enums.tencent.TencentTrtcCallBackEventEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.im.server.config.TencentTrtcConfig;
import com.xyy.saas.inquiry.im.server.controller.app.callback.vo.TencentTrtcCallBackReqVO;
import com.xyy.saas.inquiry.im.server.service.tencent.TencentTrtcClient;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

/**
 * @Author: xucao
 * @Date: 2024/12/09 15:29
 * @Description: 腾讯TRTC回调处理策略
 */
@Component
@Slf4j
public abstract class TencentTrtcCallBackHandleStrategy {

    @Resource
    private TencentTrtcClient tencentTrtcClient;

    @Resource
    private InquiryApi inquiryApi;

    @Autowired
    private TencentTrtcConfig tencentTrtcConfig;

    public String getSteamIdByInquiryPref(String roomId) {
        return tencentTrtcConfig.getRav().getBizId() + "_" + DigestUtils.md5DigestAsHex((roomId + "_main").getBytes());
    }

    /**
     * 策略执行器
     * @param callBackReqVO 回调参数
     */
    public abstract Boolean execute(TencentTrtcCallBackReqVO callBackReqVO);



    /**
     * 获取策略对应的事件
     * @return 事件
     */
    public abstract TencentTrtcCallBackEventEnum getEvent();

    /**
     * 开启混流转推到cdn
     * @param inquiryRecordDto 问诊单信息
     * @return 开启混流转推结果
     */
    public boolean startPublishStreamCdn(InquiryRecordDto inquiryRecordDto, String userId){
        log.info("准备执行混流转推...");
        // 是否已发起转推，不允许二次推流
        if (inquiryRecordDto.getStreamStatus() >= StreamStatus.STARTED_PUBLISH_STREAM.getCode()) {
            throw exception(INQUIRY_TRTC_PUBLISH_STREAM_ERROR);
        }
        String streamId = getSteamIdByInquiryPref(inquiryRecordDto.getPref());
        String taskId = tencentTrtcClient.startPublishStreamCdn(inquiryRecordDto,userId,streamId);
        if (StringUtils.isBlank(taskId)) {
            log.info("执行混流转推失败,inquiryPref:{}", inquiryRecordDto.getPref());
            throw exception(INQUIRY_TRTC_START_PUBLISH_STREAM_ERROR);
        }
        return inquiryApi.updateInquiry(InquiryRecordDto.builder().transcodingId(taskId).streamId(streamId).id(inquiryRecordDto.getId()).streamStatus(StreamStatus.STARTED_PUBLISH_STREAM.getCode()).build());
    }
}
