package com.xyy.saas.inquiry.im.server.mq.producer;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.im.server.mq.message.InquiryTrtcEndEvent;
import org.springframework.stereotype.Component;

/**
 * @ClassName：InquiryTrtcEndProducer
 * @Author: xucao
 * @Date: 2025/3/10 10:21
 * @Description: trtc视频通话结束事件消息生产者
 */
@Component
@EventBusProducer(
    topic = InquiryTrtcEndEvent.TOPIC
)
public class InquiryTrtcEndProducer extends EventBusRocketMQTemplate {

}