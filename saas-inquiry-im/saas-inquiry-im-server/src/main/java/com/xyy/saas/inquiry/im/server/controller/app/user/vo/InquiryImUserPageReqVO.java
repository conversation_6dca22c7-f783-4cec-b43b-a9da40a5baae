package com.xyy.saas.inquiry.im.server.controller.app.user.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 腾讯IM用户分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InquiryImUserPageReqVO extends PageParam {

    @Schema(description = "用户id", example = "30000")
    private Long userId;

    @Schema(description = "im用户名,唯一", example = "12053")
    private String accountId;

    @Schema(description = "用户昵称", example = "李四")
    private String nickName;

    @Schema(description = "客户端类型 0-app  1-web", example = "2")
    private Integer clientType;

    @Schema(description = "用户状态 0-正常 1-禁用", example = "1")
    private Integer userStatus;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}