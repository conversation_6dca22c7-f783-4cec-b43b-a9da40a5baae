package com.xyy.saas.inquiry.im.server.util;

import java.util.Base64;

/**
 * @ClassName：Base64Url
 * @Author: xucao
 * @Date: 2024/11/27 13:45
 * @Description: 必须描述类做什么事情, 实现什么功能
 */
public class Base64URL {
    public static byte[] base64EncodeUrl(byte[] input) {
        byte[] base64 = Base64.getEncoder().encode(input);
        for (int i = 0; i < base64.length; ++i)
            switch (base64[i]) {
                case '+':
                    base64[i] = '*';
                    break;
                case '/':
                    base64[i] = '-';
                    break;
                case '=':
                    base64[i] = '_';
                    break;
                default:
                    break;
            }
        return base64;
    }
}
