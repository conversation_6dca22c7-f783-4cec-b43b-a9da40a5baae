package com.xyy.saas.inquiry.im.server.dal.dataobject.trtc;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 问诊视频转码记录 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_transcoding")
@KeySequence("saas_inquiry_transcoding_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryTranscodingDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 问诊单号
     */
    private String inquiryPref;
    /**
     * 转码任务id
     */
    private String taskId;
    /**
     * 转码状态 0-待转码  1-未查询到文件id  2-开启转码失败  3-已开启转码  4-未查询到转码结果
     */
    private Integer status;
    /**
     * 当前调腾讯失败原因，格式 code-errmsg
     */
    private String errorInfo;

}