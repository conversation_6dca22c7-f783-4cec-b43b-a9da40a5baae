package com.xyy.saas.inquiry.im.server.api.message;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.im.enums.ErrorCodeConstants.INQUIRY_IM_PDF_GENERATE_ERROR;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.drugstore.api.tenant.TenantParamConfigApi;
import com.xyy.saas.inquiry.im.api.message.InquiryImMessageApi;
import com.xyy.saas.inquiry.im.api.message.dto.FlushImHistoryDto;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryImMessageDto;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryLastMessageDto;
import com.xyy.saas.inquiry.im.server.controller.app.message.vo.InquiryImMessagePageReqVO;
import com.xyy.saas.inquiry.im.server.controller.app.message.vo.InquiryImMessageRespVO;
import com.xyy.saas.inquiry.im.server.convert.message.InquiryImMessageConvert;
import com.xyy.saas.inquiry.im.server.service.message.InquiryImMessageService;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.signature.api.immessage.InquiryImPdfApi;
import jakarta.annotation.Resource;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * @Author: xucao
 * @Date: 2024/11/28 16:02
 * @Description: 消息相关API实现
 */
@DubboService
public class InquiryImMessageApiImpl implements InquiryImMessageApi {

    @Resource
    private InquiryImMessageService inquiryImMessageService;


    @Resource
    private InquiryApi inquiryApi;

    @Resource
    private InquiryImPdfApi inquiryImPdfApi;

    @Resource
    private TenantParamConfigApi tenantParamConfigApi;

    /**
     * 发送常规用户消息
     *
     * @param messageDto 消息对象
     * @return boolean
     */
    @Override
    public Boolean sendUserMessage(InquiryImMessageDto messageDto) {
        return inquiryImMessageService.sendUserMessage(messageDto);
    }

    /**
     * 发送系统通知消息
     *
     * @param messageDto 消息对象
     * @return
     */
    @Override
    public Boolean sendSystemMessage(InquiryImMessageDto messageDto) {
        return inquiryImMessageService.sendSystemMessage(messageDto);
    }

    /**
     * 批量发送系统通知消息
     *
     * @param messageDto 消息对象
     * @return
     */
    @Override
    public Boolean batchSendSystemMessage(InquiryImMessageDto messageDto) {
        return inquiryImMessageService.batchSendSystemMessage(messageDto);
    }


    /**
     * 批量插入消息表（不同步IM）
     *
     * @param messageDtos 消息对象集合
     * @return 插入结果
     */
    @Override
    public Boolean batchInsertMessage(List<InquiryImMessageDto> messageDtos) {
        return inquiryImMessageService.batchInsertMessage(messageDtos);
    }

    /**
     * 批量获取问诊的最后一条消息
     *
     * @param inquiryPrefs 问诊单列表
     * @return 最后一条消息集合
     */
    @Override
    public List<InquiryLastMessageDto> getLastMessage(List<String> inquiryPrefs) {
        return inquiryImMessageService.getLastMessageByInquiryPrefs(inquiryPrefs);
    }

    @Override
    public Boolean batchUpdateInquiryImHistory(List<FlushImHistoryDto> flushImHistoryDtoList) {

        if (CollectionUtils.isEmpty(flushImHistoryDtoList)) {
            return false;
        }

        List<InquiryRecordDto> inquiryRecordDtoList = flushImHistoryDtoList.stream()
            .filter(item -> item.getInquiryReportId() != null && item.getInquiryReportId() > 0 && StringUtils.isNotBlank(item.getInquiryReportPref()))
            .map(item -> new InquiryRecordDto().setId(item.getInquiryReportId()).setPref(item.getInquiryReportPref())).toList();

        if (CollectionUtils.isEmpty(inquiryRecordDtoList)) {
            return false;
        }

        return inquiryImMessageService.batchUpdateInquiryImHistory(inquiryRecordDtoList);
    }

    @Override
    public String getImInquiryPdf(String inquiryPref) {

        // 获取问诊单信息
        InquiryRecordDto inquiryDto = inquiryApi.getInquiryRecord(inquiryPref);

        // 1、根据问诊单号查询消息列表
        List<InquiryImMessageRespVO> inquiryImMessageList = inquiryImMessageService.getInquiryImMessageList(InquiryImMessagePageReqVO.builder().inquiryPref(inquiryPref).build());
        if (org.springframework.util.CollectionUtils.isEmpty(inquiryImMessageList)) {
            return "";
        }

        Integer dateType = tenantParamConfigApi.getTenantPresDateType(inquiryDto.getTenantId());

        List<com.xyy.saas.inquiry.signature.api.immessage.dto.InquiryImMessageDto> messageDtos = InquiryImMessageConvert.INSTANCE.converMsgVOLIST2DTOLIST(inquiryImMessageList, dateType);
        CommonResult<String> result = inquiryImPdfApi.generateImPdfByFreeMarker(messageDtos);
        if (result.isError()) {
            throw exception(INQUIRY_IM_PDF_GENERATE_ERROR);
        }

        return result.getData();
    }
}
