package com.xyy.saas.inquiry.im.server.mq.message;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import com.xyy.saas.inquiry.mq.inquiry.dto.InquiryEndMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @ClassName：InquiryTrtcEndEvent
 * @Author: xucao
 * @Date: 2025/3/10 10:17
 * @Description: TRTC通话完成事件
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class InquiryTrtcEndEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "INQUIRY_TRTC_END";

    private InquiryEndMessage msg;


    @JsonCreator
    public InquiryTrtcEndEvent(@JsonProperty("msg") InquiryEndMessage msg) {
        this.msg = msg;
    }

    @Override
    public String getTag() {
        return "";
    }
}