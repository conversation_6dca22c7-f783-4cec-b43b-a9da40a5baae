package com.xyy.saas.inquiry.im.server.config.webclient;

import com.xyy.saas.inquiry.im.server.config.BaseConfig;
import com.xyy.saas.inquiry.im.server.config.TencentImConfig;
import com.xyy.saas.inquiry.im.server.service.tencent.TencentImClient;
import com.xyy.saas.inquiry.im.server.util.tencent.GenerateImUserSig;
import java.net.URI;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

/**
 * @Author: xucao
 * @Date: 2024/11/28 10:12
 * @Description: 腾讯IM调用接口客户端
 */
@Slf4j
@Configuration
@EnableConfigurationProperties({TencentImConfig.class, BaseConfig.class})
public class WebClientConfig {

    @Bean
    public WebClient webClient(TencentImConfig tencentImConfig) {
        String timeOutMsg ="请求超时，请检查网络连接或服务端状态";
        // 设置更大的缓冲区大小
        ExchangeStrategies strategies = ExchangeStrategies.builder()
                // 例如，设置为1MB
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(32 * 1024 * 1024))
                .build();
        //请求超时处理器
        ExchangeFilterFunction responseTimeoutFilterFunction = ExchangeFilterFunction.ofResponseProcessor(clientResponse -> {
            if (clientResponse.statusCode().is4xxClientError() || clientResponse.statusCode().is5xxServerError()) {
                log.error("请求失败，状态码：{}，错误信息：{}", clientResponse.statusCode(), clientResponse.bodyToMono(String.class).block());
                return Mono.error(new RuntimeException(timeOutMsg));
            }
            if (clientResponse.statusCode().is2xxSuccessful()) {
                return Mono.just(clientResponse);
            }
            if (clientResponse.statusCode().is3xxRedirection()) {
                log.error("请求失败，状态码：{}，错误信息：{}", clientResponse.statusCode(), clientResponse.bodyToMono(String.class).block());
                return Mono.error(new RuntimeException(timeOutMsg));
            }
            return Mono.just(clientResponse);
        });
        // 定义一个添加查询参数的过滤器函数
        ExchangeFilterFunction addQueryParamsFilter = ExchangeFilterFunction.ofRequestProcessor(clientRequest -> {
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("sdkappid", tencentImConfig.getSdkAppId());
            queryParams.put("identifier", tencentImConfig.getAdminUserId());
            queryParams.put("usersig", GenerateImUserSig.genUserSig(tencentImConfig.getAdminUserId()));
            queryParams.put("random", System.currentTimeMillis());
            queryParams.put("contenttype", "json");


            String uri = null;
            uri = URLDecoder.decode(clientRequest.url().toString(), StandardCharsets.UTF_8);
            StringBuilder query = new StringBuilder();
            queryParams.forEach((key, value) -> query.append(key).append("=").append(value).append("&"));
            if (!query.isEmpty()) {
                query.setLength(query.length() - 1); // 移除最后一个 '&'
            }
            URI newUri = UriComponentsBuilder.fromUri(URI.create(uri))
                    .query(query.toString())
                    .build()
                    .toUri();

            return Mono.just(ClientRequest.from(clientRequest).url(newUri).build());
        });
        return WebClient.builder()
                .baseUrl(tencentImConfig.getDomain())
                .exchangeStrategies(strategies)
                .filter(addQueryParamsFilter)
                .filter(responseTimeoutFilterFunction)
                .filter(ExchangeFilterFunction.ofRequestProcessor(request -> {
                    // 请求处理
                    ClientRequest clientRequest = ClientRequest.from(request)
                            .build();
                    log.info("请求地址：{}", clientRequest.url());
                    return Mono.just(clientRequest);
                }).andThen(ExchangeFilterFunction.ofResponseProcessor(response -> {
                    log.info("响应状态码：{}", response.statusCode());
                    // 响应处理
                    if (response.statusCode() == HttpStatus.REQUEST_TIMEOUT) {
                        return Mono.error(new RuntimeException(timeOutMsg));
                    }
                    //处理连接异常
                    if (response.statusCode().is4xxClientError() || response.statusCode().is5xxServerError()) {
                        return Mono.error(new RuntimeException("请求失败，状态码：" + response.statusCode() + "，错误信息：" + response.bodyToMono(String.class).block()));
                    }
                    return Mono.just(response);
                }))).build();
    }



    @Bean
    public TencentImClient tencentImClient(WebClient webClient , BaseConfig baseConfig) {
        WebClientAdapter webClientAdapter = WebClientAdapter.create(webClient);
        //设置超时时间
        Duration timeout = Duration.ofMillis(baseConfig.getHttpTimeout() * 10000L);
        webClientAdapter.setBlockTimeout(timeout);

        HttpServiceProxyFactory httpServiceProxyFactory = HttpServiceProxyFactory
                .builderFor(webClientAdapter)
                .build();
        return httpServiceProxyFactory.createClient(TencentImClient.class);
    }
}
