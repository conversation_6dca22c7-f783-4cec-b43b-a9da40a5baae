package com.xyy.saas.inquiry.im.server.service.user;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.enums.inquiry.ClientChannelTypeEnum;
import com.xyy.saas.inquiry.im.enums.DeviceTypeEnum;
import com.xyy.saas.inquiry.im.server.controller.app.user.vo.InquiryImUserPageReqVO;
import com.xyy.saas.inquiry.im.server.controller.app.user.vo.InquiryImUserRespVO;
import com.xyy.saas.inquiry.im.server.controller.app.user.vo.InquiryImUserSaveReqVO;
import com.xyy.saas.inquiry.im.server.dal.dataobject.user.InquiryImUserDO;
import jakarta.validation.Valid;
import java.util.List;


/**
 * 腾讯IM用户 Service 接口
 *
 * <AUTHOR>
 */
public interface InquiryImUserService {

    /**
     * 创建腾讯IM用户
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInquiryImUser(@Valid InquiryImUserSaveReqVO createReqVO);

    /**
     * 更新腾讯IM用户
     *
     * @param updateReqVO 更新信息
     */
    void updateInquiryImUser(@Valid InquiryImUserSaveReqVO updateReqVO);

    /**
     * 删除腾讯IM用户
     *
     * @param id 编号
     */
    void deleteInquiryImUser(Long id);

    /**
     * 获得腾讯IM用户
     *
     * @param id 编号
     * @return 腾讯IM用户
     */
    InquiryImUserRespVO getInquiryImUser(Long id , ClientChannelTypeEnum clientChannelTypeEnum, DeviceTypeEnum deviceTypeEnum);


    /**
     * 获得腾讯IM用户分页
     *
     * @param pageReqVO 分页查询
     * @return 腾讯IM用户分页
     */
    PageResult<InquiryImUserDO> getInquiryImUserPage(InquiryImUserPageReqVO pageReqVO);

    /**
     * 根据用户id和客户端类型查询im账号
     * @param userId 用户id
     * @param clientChannelTypeEnum 客户端类型
     * @return 用户IM账号
     */
    String queryUserImAccountId(Long userId, ClientChannelTypeEnum clientChannelTypeEnum);

    /**
     * 根据用户id和客户端类型批量查询im账号
     * @param list
     * @param clientChannelTypeEnum
     * @return
     */
    List<String> queryUserImAccountList(List<Long> list, ClientChannelTypeEnum clientChannelTypeEnum);
}