package com.xyy.saas.inquiry.im.server.controller.app.callback.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.xyy.saas.inquiry.annotation.LocalDateTimeDeserializer;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * @Author: xucao
 * @Date: 2024/11/28 17:46
 * @Description: 腾讯IM回调请求入参
 */
@Data
public class TencentImCallBackReqVO implements Serializable {

    @JsonProperty("CallbackCommand")
    private String callbackCommand;

    @JsonProperty("From_Account")
    private String fromAccount;

    @JsonProperty("To_Account")
    private String toAccount;

    @JsonProperty("Report_Account")
    private String reportAccount;

    @JsonProperty("Peer_Account")
    private String peerAccount;

    @JsonProperty("LastReadTime")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime lastReadTime;

    @JsonProperty("MsgSeq")
    private Long msgSeq;

    @JsonProperty("MsgRandom")
    private Long msgRandom;

    @JsonProperty("MsgTime")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime msgTime;

    @JsonProperty("MsgKey")
    private String msgKey;

    @JsonProperty("MsgId")
    private String msgId;

    @JsonProperty("OnlineOnlyFlag")
    private Long onlineOnlyFlag;

    @JsonProperty("SendMsgResult")
    private Long sendMsgResult;

    @JsonProperty("ErrorInfo")
    private String errorInfo;

    @JsonProperty("MsgBody")
    private List<MessageBody> msgBody;

    @JsonProperty("CloudCustomData")
    private String cloudCustomData;

    @JsonProperty("EventTime")
    private long eventTime;

    @Data
    public static class MessageBody {
        @JsonProperty("MsgType")
        private String msgType;

        @JsonProperty("MsgContent")
        private MessageContent msgContent;
    }

    @Data
    public static class MessageContent {
        @JsonProperty("Text")
        private String text;

    }

    @Data
    public static class MsgExt {

        /**
         * 问诊编号
         */
        private String inquiryPref;

        /**
         * im消息类型
         */
        private String sourceType;

        /**
         * 图片链接
         */
        private String img;

        /**
         * url链接
         */
        private String url;
    }

}
