package com.xyy.saas.inquiry.im.server.dal.dataobject.tencent.in;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName：InquiryImUserConvert
 * @Author: xucao
 * @Date: 2024/11/28 14:10
 * @Description: 腾讯IM 发送消息DO
 */
@Data
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TencentImSendMessageDO implements Serializable {

    @JSONField(name = "SyncOtherMachine")
    private Integer syncOtherMachine;

    @JSONField(name = "From_Account")
    private String fromAccount;

    @JSONField(name = "To_Account")
    private String toAccount;

    @JSONField(name = "MsgRandom")
    private Long msgRandom;

    @JSONField(name = "ForbidCallbackControl")
    private List<String> forbidCallbackControl;

    @JSONField(name = "MsgSeq")
    private Long msgSeq;

    @JSONField(name = "MsgBody")
    private List<MsgBody> msgBody;

    @JSONField(name = "CloudCustomData")
    private String cloudCustomData;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MsgBody implements Serializable {

        @JSONField(name = "MsgType")
        private String msgType;

        @JSONField(name = "MsgContent")
        private MsgContent msgContent;


        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class MsgContent implements Serializable {

            @JSONField(name = "Text")
            private String text;
        }
    }

    @JSONField(name = "IsNeedReadReceipt")
    private Integer isNeedReadReceipt;
}
