package com.xyy.saas.inquiry.im.api.message.dto;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: xucao
 * @Date: 2025/2/11 15:16
 * @Description: 医生卡片扩展消息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ImDoctorCardMessageExtDto implements Serializable{
    private String inquiryPref;
    @Builder.Default
    private Boolean newInquiry = true;
    private String sourceType;
    private ImDoctorCardMessageExtDto.DoctorInfo doctorInfo;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class DoctorInfo implements Serializable {

        private String pref;

        private String name;

        private String titleName;

        private String photo;

        private LocalDateTime startPracticeTime;

        private String professionalNo;

        private Integer practiceTime;

        private String receptionNum;

        private String goodCommentRate;
    }
}
