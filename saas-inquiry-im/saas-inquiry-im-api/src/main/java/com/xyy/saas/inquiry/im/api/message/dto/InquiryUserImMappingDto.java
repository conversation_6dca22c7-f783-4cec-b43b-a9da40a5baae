package com.xyy.saas.inquiry.im.api.message.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;

/**
 * @Author: xucao
 * @DateTime: 2025/5/20 22:41
 * @Description:  问诊用户IM映射
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InquiryUserImMappingDto implements Serializable {

    /**
     * 问诊单编码
     */
    private String inquiryPref;

    /**
     * 患者userId
     */
    private Long patientUserId;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 医生userId
     */
    private Long doctorUserId;

    /**
     * 医生姓名
     */
    private String doctorName;
}
