package com.xyy.saas.inquiry.im.api.message.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName:InquiryImMessageDto
 * @Author: xucao
 * @Date: 2024/11/28 15:55
 * @Description: 必须描述类做什么事情, 实现什么功能
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InquiryImMessageDto implements Serializable {

    // 发送方用户id
    private String fromAccount;

    // 接收方用户id
    private String toAccount;

    // 群发场景接受方IM账号列表
    private List<String> toAccountList;

    // 消息内容
    private String msg;

    // 发送时间
    private LocalDateTime sendTime;

    // 扩展信息
    private Object extDto;

    // 问诊单号
    private String inquiryPref;
}
