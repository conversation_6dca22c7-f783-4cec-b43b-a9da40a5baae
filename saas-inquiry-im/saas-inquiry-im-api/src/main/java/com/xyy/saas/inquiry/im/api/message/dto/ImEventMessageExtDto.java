package com.xyy.saas.inquiry.im.api.message.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ImEventMessageExtDto implements Serializable {

    /**
     * 事件类型
     */
    private String eventType;

    @Builder.Default
    private Boolean newInquiry = true;

    /**
     * 事件信息
     */
    private EventInfo eventInfo;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class EventInfo implements Serializable {

        /**
         * 问诊单号
         */
        private String inquiryPref;

        /**
         * 处方编号
         */
        private String prescriptionPref;

        /**
         * 是否自动开方  0-否  1 是
         */
        private Integer autoInquiry;

        /**
         * 预问诊单号
         */
        private String preInquiryPref;

        /**
         * 审核结果
         */
        private Integer auditStatus;

        /**
         * 视频处理结果 1:接听 2:挂断
         */
        private Integer videoHandleStatus;

        /**
         * 视频通话发起方动作 1:发起 2:取消
         */
        private Integer startActionType;

        /**
         * 领方药师名称
         */
        private String pharmacistName;

        /**
         * 患者姓名
         */
        private String patientName;
    }
}
