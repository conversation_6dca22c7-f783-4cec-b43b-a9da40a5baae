package com.xyy.saas.inquiry.im.api.user;

import com.xyy.saas.inquiry.enums.inquiry.ClientChannelTypeEnum;
import java.util.List;

/**
 * @Author: xucao
 * @Date: 2025/01/20 16:10
 * @Description: IM 用户相关接口
 */
public interface InquiryImUserApi {

    /**
     * 根据医生编码获取医生IM账号
     *
     * @param doctorPref 医生编码
     * @return IM账号
     */
    String getDoctorImAccountByDoctorPref(String doctorPref);


    /**
     * 根据医生编码获取医生IM账号
     *
     * @param doctorPrefList
     * @return
     */
    List<String> getDoctorImAccountListByDoctorPrefList(List<String> doctorPrefList);

    /**
     * 根据userId + 客户端类型 获取IM账号
     *
     * @param userId            用户id
     * @param clientChannelType 客户端类型
     * @return IM账号
     */
    String getImAccountByUserIdAndClientType(Long userId, Integer clientChannelType);


    /**
     * 获得腾讯IM用户
     *
     * @param id 编号
     * @return 腾讯IM用户
     */
    String getAndCreateInquiryImUser(Long userId, ClientChannelTypeEnum clientChannelType);

    /**
     * 根据 userIds + 客户端类型 获取IM账号
     *
     * @param userIds
     * @param clientChannelType
     * @return
     */
    List<String> queryUserImAccountList(List<Long> userIds, ClientChannelTypeEnum clientChannelType);
}
