package com.xyy.saas.inquiry.im.api.message.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: xucao
 * @Date: 2025/2/10 22:02
 * @Description: im 消息扩展字段类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ImApplyMessageExtDto implements Serializable {
    private String inquiryPref;
    private String sourceType;
    @Builder.Default
    private Boolean newInquiry = true;
    private DrugInfo drugInfo;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class DrugInfo implements Serializable{
        private String patientName;
        private int patientSex;
        private String patientAge;
        private List<String> mainSuit;
        private List<String> diagnosisName;
        private List<String> allergicItem;
        private int liverKidneyValue;
        private int gestationLactationValue;
        private int medicineType;
        private List<InquiryProductInfo> inquiryProductInfos;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class InquiryProductInfo implements Serializable{
        // Add fields as per the structure of InquiryProductInfo
        // Example:
        private BigDecimal productNum;
        private String productName;
        // Add other fields as needed
    }

}
