package com.xyy.saas.inquiry.im.api.message.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FlushImHistoryDto implements Serializable {

    /**
     * 问诊记录id
     */
    private Long inquiryReportId;

    /**
     * 问诊记录编号
     */
    private String inquiryReportPref;

}
