package com.xyy.saas.inquiry.patient.es.inquiry;

import cn.hutool.core.collection.CollUtil;
import com.xyy.saas.binlog.core.DataCompositionService;
import com.xyy.saas.binlog.core.data.DataSource;
import com.xyy.saas.binlog.core.data.DataSource.OperationType;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordDetailPageReqVO;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDetailDO;
import com.xyy.saas.inquiry.patient.dal.mysql.inquiry.InquiryRecordDetailMapper;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 处方表ES数据组装
 *
 * @Author:chenxiaoyi
 * @Date:2025/08/29 11:14
 */
@Service
@Slf4j
public class InquiryRecordCompositionService implements DataCompositionService {

    private final String INQUIRY_RECORD_TABLE = "saas_inquiry_record";

    private final String PREF = "pref";

    private final String PRESCRIPTION_TYPE = "prescriptionType";

    @Resource
    private InquiryRecordDetailMapper inquiryRecordDetailMapper;

    @Override
    public boolean isComposition(DataSource dataSource) {
        if (dataSource == null || CollUtil.isEmpty(dataSource.data()) || Objects.equals(OperationType.DELETE, dataSource.operationType())) {
            return false;
        }
        return StringUtils.equals(dataSource.table(), INQUIRY_RECORD_TABLE);
    }

    @Override
    public void executeComposition(DataSource dataSource) {

        List<String> prefs = dataSource.data().stream().map(map -> Optional.ofNullable(map.get(PREF)).orElse("").toString())
            .filter(StringUtils::isNotBlank).collect(Collectors.toList());

        if (CollUtil.isEmpty(prefs)) {
            return;
        }
        // 获取组装明细产品名称
        List<InquiryRecordDetailDO> inquiryPrescriptionDetailDOS = inquiryRecordDetailMapper.selectListByCondition(new InquiryRecordDetailPageReqVO().setInquiryPrefList(prefs));
        if (CollUtil.isEmpty(inquiryPrescriptionDetailDOS)) {
            return;
        }

        Map<String, InquiryRecordDetailDO> detailProcuctNameMap = inquiryPrescriptionDetailDOS.stream()
            .collect(Collectors.toMap(InquiryRecordDetailDO::getInquiryPref, Function.identity(), (a, b) -> b));

        for (Map<String, Object> datum : dataSource.data()) {
            Optional.ofNullable(detailProcuctNameMap.get(datum.get(PREF).toString())).ifPresent(detail -> {
                datum.put(PRESCRIPTION_TYPE, detail.getPrescriptionType());
            });
        }
    }
}
