package com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 问诊记录详情分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class InquiryRecordDetailPageReqVO extends PageParam {

    @Schema(description = "问诊单pref")
    private String inquiryPref;

    @Schema(description = "问诊单号列表")
    private List<String> inquiryPrefList;

    /**
     * 租户编号
     */
    private Long tenantId;

    @Schema(description = "肝肾功能异常  0、无  1、肝功能异常  2、肾功能异常  3、肝肾功能异常")
    private Integer liverKidneyValue;

    @Schema(description = "妊娠哺乳期   0、否  1、妊娠期   2、哺乳期  3、妊娠哺乳期")
    private Integer gestationLactationValue;

    @Schema(description = "患者pref")
    private String patientPref;

    @Schema(description = "患者姓名", example = "赵六")
    private String patientName;

    @Schema(description = "患者性别：1 男 2 女")
    private Integer patientSex;

    @Schema(description = "患者年龄")
    private String patientAge;

    @Schema(description = "患者手机号")
    private String patientMobile;

    @Schema(description = "慢病病情需要 0 否  1是")
    private Integer slowDisease;

    @Schema(description = "患者身份证号")
    private String patientIdCard;

    @Schema(description = "主诉")
    private String mainSuit;

    @Schema(description = "过敏史  eg：青霉素|头孢")
    private String allergicItem;

    @Schema(description = "诊断编码")
    private List<String> diagnosisCode;

    @Schema(description = "诊断说明", example = "王五")
    private String diagnosisName;

    @Schema(description = "个人史")
    private String patientHisDesc;

    @Schema(description = "现病史")
    private String currentIllnessDesc;

    @Schema(description = "线下就医处方或病历图片")
    private List<String> offlinePrescriptions;

    @Schema(description = "预购药明细")
    private InquiryProductDto preDrugDetail;

    @Schema(description = "备注说明")
    private String remarks;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}