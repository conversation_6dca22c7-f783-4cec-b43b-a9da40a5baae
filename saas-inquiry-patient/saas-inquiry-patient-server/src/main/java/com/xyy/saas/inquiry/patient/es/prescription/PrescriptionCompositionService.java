package com.xyy.saas.inquiry.hospital.server.es.prescription;

import cn.hutool.core.collection.CollUtil;
import com.xyy.saas.binlog.core.DataCompositionService;
import com.xyy.saas.binlog.core.data.DataSource;
import com.xyy.saas.binlog.core.data.DataSource.OperationType;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDetailDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.prescription.InquiryPrescriptionDetailMapper;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 处方表ES数据组装
 *
 * @Author:chenxiaoyi
 * @Date:2025/08/29 11:14
 */
@Service
@Slf4j
public class PrescriptionCompositionService implements DataCompositionService {

    private final String PRESCRIPTION_TABLE = "saas_inquiry_prescription";

    private final String PRESCRIPTION_PREF = "pref";

    private final String PRODUCT_NAME = "productName";


    @Resource
    private InquiryPrescriptionDetailMapper inquiryPrescriptionDetailMapper;

    @Override
    public boolean isComposition(DataSource dataSource) {
        if (dataSource == null || CollUtil.isEmpty(dataSource.data()) || Objects.equals(OperationType.DELETE, dataSource.operationType())) {
            return false;
        }
        return StringUtils.equals(dataSource.table(), PRESCRIPTION_TABLE);
    }

    @Override
    public void executeComposition(DataSource dataSource) {

        List<String> prefs = dataSource.data().stream().map(map -> Optional.ofNullable(map.get(PRESCRIPTION_PREF)).orElse("").toString())
            .filter(StringUtils::isNotBlank).collect(Collectors.toList());

        if (CollUtil.isEmpty(prefs)) {
            return;
        }
        // 获取组装明细产品名称
        List<InquiryPrescriptionDetailDO> inquiryPrescriptionDetailDOS = inquiryPrescriptionDetailMapper.selectByPrescriptionPrefs(prefs);
        if (CollUtil.isEmpty(inquiryPrescriptionDetailDOS)) {
            return;
        }

        Map<String, String> detailProcuctNameMap = inquiryPrescriptionDetailDOS.stream()
            .collect(Collectors.groupingBy(InquiryPrescriptionDetailDO::getPrescriptionPref
                , Collectors.mapping(InquiryPrescriptionDetailDO::getCommonName, Collectors.joining(","))));

        for (Map<String, Object> datum : dataSource.data()) {
            String productName = detailProcuctNameMap.get(datum.get(PRESCRIPTION_PREF).toString());
            datum.put(PRODUCT_NAME, productName);
        }
    }
}
