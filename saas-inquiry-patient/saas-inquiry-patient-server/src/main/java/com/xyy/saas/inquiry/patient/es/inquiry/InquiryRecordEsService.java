package com.xyy.saas.inquiry.patient.es.inquiry;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordRespVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 问诊表ES数据查询
 *
 * @Author:chenxiaoyi
 * @Date:2025/08/29 11:14
 */
@Service
@Slf4j
public class InquiryRecordEsService {

    PageResult<InquiryRecordRespVO> getEsInquiryRecordPage(InquiryRecordPageReqVO pageReqVO) {

        BoolQuery.Builder boolQuery = new BoolQuery.Builder();

        return PageResult.empty();
    }
}
