package com.xyy.saas.inquiry.hospital.server.es.prescription;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionRespVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 处方表ES数据查询
 *
 * @Author:chenxiaoyi
 * @Date:2025/08/29 11:14
 */
@Service
@Slf4j
public class PrescriptionEsService {


    PageResult<InquiryPrescriptionRespVO> getEsInquiryPrescriptionPage(InquiryPrescriptionPageReqVO queryVo) {

        BoolQuery.Builder boolQuery = new BoolQuery.Builder();

        return PageResult.empty();
    }
}
